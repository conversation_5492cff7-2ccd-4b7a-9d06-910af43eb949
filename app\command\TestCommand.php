<?php
declare (strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;

class TestCommand extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('testCommand')
            ->setDescription('the test command');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('the test command');
    }
}
