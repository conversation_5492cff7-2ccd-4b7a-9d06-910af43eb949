<?php
namespace app\exception;
use think\Exception;

/**
 * Class BaseException
 * 自定义异常类的基类
 */
class BaseException extends Exception
{
    public $message = '系统异常';
    public $errorCode = '10002';
    public $data = [];

    /**
     * 构造函数，接收一个关联数组
     * @param array $params
     */
    public function __construct($params=[])
    {
        if(!is_array($params)){
            return;
        }
        if(array_key_exists('error_msg',$params)){
            $this->message = $params['error_msg'];
        }
        if(array_key_exists('error_code',$params)){
            $this->errorCode = $params['error_code'];
        }
        if(array_key_exists('data',$params)){
            $this->data = $params['data'];
        }
    }
}