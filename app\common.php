<?php
// 应用公共文件

use think\facade\Cache;
use think\Response;
use think\facade\Log;
use think\facade\Db;
use PhpOffice\PhpSpreadsheet\Reader\Xls;

if (!function_exists('throwResponse')) {
    /**
     * 接口获取返回方法
     * @param int $code 状态码
     * @param string $error_msg 错误信息
     * @param array $options 返回参数
     * @param array $header 返回头信息
     * @return Response
     */
    function throwResponse($options = [], int $code = 0, string $error_msg = '', array $header = []): Response
    {
        $data['error_code']  = $code;
        $data['error_msg']  = $error_msg;
        $data['data'] = $options;
        return Response::create($data, 'json', 200)->header($header);
    }
}

/**
 *Description:获取nacos配置
 *Author:zrc
 *Date:2021/8/6
 *Time:16:59
 *@paramstring$dataId
 *@paramstring$group
 *@paramstring$tenant
 *@returnarray|mixed|string
 */
function getConfigs($dataId='misc',$group='vinehoo.conf'){
    $url = env('NACOS.URL').'?dataId='.$dataId.'&group='.$group.'&tenant='.env('NACOS.TENANT').'&password='.env('NACOS.PASSWORD').'&username='.env('NACOS.USERNAME1');
    $result=json_decode(get_url($url),true);
    if(isset($result['status'])&&$result['status']>=400){
        $data['error_code']=\app\ErrorCode::PARAM_ERROR;
        $data['error_msg']='未获取到nacos配置:'.$result['message'];
        $data['data']=[];
        $result=json_encode($data,JSON_UNESCAPED_UNICODE);
        print_r($result);
        die;
    }
    return $result;
}


/**
 * 定时任务计划
 * @param $task_trigger_time "触发时间"
 * @param $task_url "触发访问的连接"
 * @param string $task_data "调用接口时使用POST方式传递给接口的数据"
 * @return mixed|string
 */
function timedTaskadd($task_trigger_time,$task_url,$task_data = '{"id":"-1"}'){
    $url = env('ITEM.SLS_URL').'/services/v3/task/add';
    //$url = 'http://192.168.4.156:8888/task/add';
    $params['task_id'] = uniqid();
    $params['task_trigger_time'] = $task_trigger_time;
    $params['task_url'] = $task_url;
    $params['task_data'] = $task_data;
    $data = json_encode($params,JSON_UNESCAPED_UNICODE);
    $response = http_post_json($url,$data);
    $response = json_decode($response,true);
    if($response['error_code'] == 0){
        return $params['task_id'];
    }else{
        return $response;
    }
}

/**
 * @param $old_task_id 需要修改的任务id
 * @param $task_trigger_time 触发时间
 * @param $task_url 触发访问的连接
 * @param $data 调用接口时使用POST方式传递给接口的数据
 * @return bool
 */
function timedTaskedit($old_task_id,$task_trigger_time,$task_url,$data = '{"id":"-1"}'){
    $url = env('ITEM.SLS_URL').'/services/v3/task/update';
    //$url = 'http://192.168.4.156:8888/task/update';
    $params['old_task_id'] = $old_task_id;
    $params['new_task_id'] = uniqid();
    $params['task_trigger_time'] = $task_trigger_time;
    $params['task_url'] = $task_url;
    $params['task_data'] = $data;
    $data = json_encode($params,JSON_UNESCAPED_UNICODE);
    $response = http_post_json($url,$data);
    $response = json_decode($response,true);
    if($response['error_code'] == 0){
        return $params['task_id'];
    }else{
        return $response;
    }
}

/**
 * @param $task_id 需要删除定时任务的task_id
 * @return bool
 */
function timedTaskdel($task_id){
    $url = env('ITEM.SLS_URL').'/services/v3/task/delete';
    //$url = 'http://192.168.4.156:8888/task/delete';
    $params['task_id'] = $task_id;
    $data = json_encode($params,JSON_UNESCAPED_UNICODE);
    $response = http_post_json($url,$data);
    $response = json_decode($response,true);
    if($response['error_code'] == 0){
        return true;
    }else{
        return false;
    }
}


/**
 * 网络文件下载
 * @param $webpath 网络下载地址
 * @param $savepath 本地保存路径
 * @param $saveName 保存名
 * @return bool
 */
function downloadFile($webpath,$savepath = null,$saveName = null){
    try {
        $savepath = !empty($savepath)?$savepath:'./upload/'.date('Y-m-d',time()).'/';
        $ext = substr($webpath,strrpos($webpath, '.'));
        $saveName = !empty($saveName)?$saveName:date('His',time()).'_'.uniqid().$ext;
        if(!is_dir($savepath)){
            mkdir($savepath,0777,true);
        }
        $filePathName = $savepath.$saveName;
        file_put_contents($filePathName,file_get_contents($webpath));
        $filePathName = ltrim($filePathName,'.');
        $filePathName = app()->getRootPath() . 'public'.$filePathName;
        $data['status'] = true;
        $data['path'] = $filePathName;
        return $data;
    }catch(Exception $exception){
        $message = $exception->getMessage();
        Log::write($message,'error');
        $data['status'] = false;
        $data['msg'] = $message;
        return $data;
    }
}



/**
 * 获取excel里面的电话号码
 * @param $path
 * @return array
 * @throws PHPExcel_Exception
 * @throws PHPExcel_Reader_Exception
 */
function readPhoneList($path){
    $type = pathinfo($path);
    $type = strtolower($type["extension"]);
    if ($type == 'xlsx') {
        $type = 'Excel2007';
    } elseif ($type == 'xls') {
        $type = 'Excel5';
    }

    //todo  $type =    Excel5
    //      运行到下一步 报错 Array and string offset access syntax with curly braces is deprecated

    $objReader = PHPExcel_IOFactory::createReader($type);
    $objPHPExcel = $objReader->load($path);
    $sheet = $objPHPExcel->getSheet(0);//获取工作薄
    // 取得总行数
    $highestRow = $sheet->getHighestRow();
    //循环读取excel文件,读取一条,插入一条
    $data=array();
    //从第一行开始读取数据  这里类似冒泡算法
    for($j=1;$j<=$highestRow;$j++){
        $data[]=$objPHPExcel->getActiveSheet()->getCell("A$j")->getFormattedValue();
    }
    return $data;
}

/**
 * http post请求
 * @param string $url
 * @param array $params
 * @param array $configs
 * @param string $contentType
 * @return array|mixed
 */
function httpPost(string $url, array $params = [], array $configs = [], string $contentType = 'form_params'){
    $timeout = env('TIMEOUT',5);
    $configs['timeout'] = $configs['timeout'] ?? $timeout;
    $client = new \GuzzleHttp\Client($configs);
    $params = [$contentType => $params];

    try {
        $request = $client->request('POST', $url, $params);

        $return = $request->getBody()->getContents();
    } catch (RequestException $e) {
        $message = $e->getMessage();
        $return = [
            'status' =>'fail',
            'errorCode'=>'-1',
            'msg'=>$message,
            'data'=> []
        ];
    }
    if(!is_array($return)){
        $response = json_decode($return, true);
    }else{
        $response = $return;

    }
    return $response;
}

function http_post_json($url,$data){
    if (empty($url) || empty($data)) {
        return false;
    }
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Content-Length: ' . strlen($data),
        'vinehoo-client:ok-tp6-sms'
    ));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //curl_setopt($ch, CURLOPT_BINARYTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    $res = curl_exec($ch);
    //$err = curl_errno($ch);
    curl_close($ch);
    return $res;
}

/**
 * @Describe:http get请求
 * <AUTHOR>
 * @Date 2021/9/15 10:42
 * @param $url
 * @return bool|string
 */
function http_get($url){
    $ch = curl_init();
    curl_setopt($ch,CURLOPT_URL,$url);
    curl_setopt($ch,CURLOPT_SSL_VERIFYHOST,false);
    curl_setopt($ch,CURLOPT_SSL_VERIFYPEER,false);
    curl_setopt($ch,CURLOPT_RETURNTRANSFER,1);
    $output = curl_exec($ch);
    curl_close($ch);
    return $output;
}


/**
 * @param $message_id
 * @return bool|string
 * @throws \think\db\exception\DataNotFoundException
 * @throws \think\db\exception\DbException
 * @throws \think\db\exception\ModelNotFoundException
 */
function dingdingCreate($message_id){
//    $url = 'http://dingtalk-approval.vinehoo.com/dingtalk/v3/approval/create';

    $url = env('ITEM.DINTALK_SYSTEM_NOTICE_URL') . '/dingtalk/v3/process/instance/create';
    $message_info = Db::name('messages')->where([['id','=',$message_id]])->find();
    $dept_id = request()->header('dingtalk-dept-id');
    $dingtalk_uid = request()->header('dingtalk-uid');
    if(empty($dept_id) || empty($dingtalk_uid)){
        $result['error_msg'] = '请传入发起人钉钉部门id和钉钉用户uid';
        return $result['error_msg'];
    }
    $form_component_values[] = [
        'name'=>'审批ID',
        'value'=>strval(mt_rand(100000000,999999999))
    ];
    $form_component_values[] = [
        'name'=>'短信ID',
        'value'=>strval($message_id)
    ];
    $form_component_values[] = [
        'name'=>'短信内容',
        'value'=>$message_info['sms_content']
    ];
    $form_component_values[] = [
        'name'=>'接收人数',
        'value'=>strval($message_info['sms_count'])
    ];
    $form_component_values[] = [
        'name'=>'计划发送时间',
        'value'=>strval($message_info['sms_schedule'])
    ];
    $data['form_component_values'] = $form_component_values;
    $data['process_code'] = env("SMS.APPROVAL_PROCESS_CODE");
    $data['dept_id'] = $dept_id;
    $data['originator_user_id'] = strval($dingtalk_uid);
    $data = json_encode($data,JSON_UNESCAPED_UNICODE);
    Log::info("审批参数：".$data);
    $result = http_post_json($url,$data);
    $result = json_decode($result,true);
    if($result['error_code'] === 0){
        return true;
    }else{
        $error_msg = isset($result['error_msg'])?$result['error_msg']:"";
        return "审批发起失败：".$error_msg;
    }
}




/**
 * @param $PersonalStr 个性化短息字符串
 * @param $dataInfo 接口查询需要替换的数据,必须带手机号
 * @return
 */
function createPersonalData($PersonalStr,$dataInfo){
    $regex = '/(?<=\{)(.*?)(?=})/';
    preg_match_all($regex,$PersonalStr,$feild);
    if(!empty($feild[0])){
        $feild = array_flip($feild[0]);
        $feild = array_flip($feild);
        $feild = array_merge($feild);
    }else{
        $data['status'] = false;
        $data['msg'] = '未匹配到添加字符';
    }
    $data['status'] = true;
    $data['phoneList'] = [];
    $data['smsContentList'] = [];
    foreach($dataInfo as $value){
        $data['phoneList'][] = $value['phone'];
        foreach($feild as $val){
            $replaceArr['{'.$val.'}'] = $value[$val];
            $replaceArr['{'.$val.'}'] = $value[$val];
        }
        $data['smsContentList'][] = strtr($PersonalStr,$replaceArr);
    }
    return $data;
}

/**
 * redis sort-set数组批量导入
 * @param $key redis的key值
 * @param $arr 需要加入redis的数组
 */
function redisBatchAdd($key,$arr){
    $redistData[] = $key;
    foreach($arr as $key=>$value){
        $redistData[] = $key;
        $redistData[] = $value;
    }
    call_user_func_array([Cache::store('redis')->handler(), 'zadd'], $redistData);
}

/**
 * 上传文件重名
 * @return string
 */
function generateName(){
    return date('Y-m-d') . '/' . date('His').mt_rand(100, 999);
}

/**
 * 数组转化字符串
 * @param $array 需要转化的数组
 * @param $p 分隔符","
 * @return string
 */
function arrayToStr($array, $p = ','){
    return implode($p, $array);
}

/**
 * @param $string 需要分割的字符串
 * @param string $p 分割符","
 * @return false|string[]
 */
function strToArray($string, $p = ','){
    return explode($p, $string);
}

/**
 * @Describe:检验传入参数
 * <AUTHOR>
 * @Date 2021/9/7 16:31
 * @param $data 检验数据
 * @param $rule 检验规则
 * @param $message 错误提示
 * @return array|string
 */
function checkParam(array $data,array $rule,array $message){
    $validate = \think\facade\Validate::rule($rule);
    $validate->message($message);
    if($validate->check($data)){
        return true;
    }else{
        $returnData['data'] = [];
        $returnData['error_code'] = 10001;
        $returnData['error_msg'] = $validate->getError();
        return $returnData;
    }
}
function getAdminInfo($admin_id)
{
    $url = env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info?admin_id='.$admin_id;
    $admin = http_get($url);
    $result = json_decode($admin,true);
    if (isset($result['error_code']) && $result['error_code'] == 0){
        return $result['data'];
    }
    return [];
}
/**
 * @方法描述:url请求重试
 * @param string $url 请求url
 * @param array $data 请求参数
 * @param array $haeder 请求头
 * @param string $method 请求的方式
 * @param int $timeout 超时时间，单位s
 * @return Response
 */
function curlRetryRequest($url, $data = [], $haeder = [], $method = 'POST', $timeout = 3, $i = 0)
{
    if (empty($haeder)){
        $haeder = array(
            "vinehoo-client: content-audit",
        );
    }
    $res = curlRequest($url, $data, $haeder, $method, $timeout) ?? '';
    $res = json_decode($res,true);
    if (!isset($res['error_code']) && $i<3) {
        $i++;
        $res = curlRetryRequest($url, $data, $haeder, $method, $timeout, $i);
    }
    return $res;
}

/**
 * @方法描述:url请求
 * @param string $url 请求url
 * @param array $data 请求参数
 * @param array $haeder 请求头
 * @param string $method 请求的方式
 * @param int $timeout 超时时间，单位s
 * @param bool $sync 不启用异步，默认为true
 * @return Response
 */
function curlRequest($url, $data = [], $haeder = [], $method = 'POST', $timeout = 3, $sync = True)
{
    $ch = curl_init();
    if (is_array($data)) {
        $data = http_build_query($data);
    }
    if (empty($haeder)){
        $haeder = array(
            "Content-Type: application/json;charset=utf-8",
            "Accept: application/json",
            "Content-Length:".strlen($data)
        );
    }

    curl_setopt($ch, CURLOPT_HTTPHEADER,$haeder);
    if ($method == 'POST') {
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, True);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    } else {
        if (!empty($data)) curl_setopt($ch, CURLOPT_URL, "{$url}?{$data}");
        else curl_setopt($ch, CURLOPT_URL, $url);
    }
    if (strlen($url) > 5 && strtolower(substr($url, 0, 5)) == "https") {
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    }
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    if ($haeder) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $haeder);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, $sync);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_NOBODY, !$sync);
    $return = curl_exec($ch);
    curl_close($ch);
    return $return;
}

function getMillisecond(){
    list($msec, $sec) = explode(' ', microtime());
    $msectime =  (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);
    return $msectimes = substr($msectime,0,13);
}

function cryptionDeal($param, $type = 'D')
{
    $url  = $type == 'D' ? '/v1/decrypt' : '/v1/encrypt';

    $data = [
        'orig_data' => array_str(array_values($param['orig_data'])),
        'from'      => 'tp6-sms',
        'uid'       => strval($param['uid']),
        'operator'  => strval($param['operator'])
    ];
    $res = curlRetryRequest(env('ITEM.CRYPTION_ADDRESS') . $url, json_encode($data));
    return $res['data'] ?? [];
}
function array_str($Input)
{
    if (!is_array($Input)) {
        return strval(trim($Input));
    }
    return array_map('array_str', $Input);
}

/** 拉去远端文件 */
function download_image($url, $format = 'xlsx')
{
    $file           = file_get_contents($url);
    $time           = time();
    $localPath      = root_path() . 'public/storage/file';
    $pic_local_path = $localPath . '/' . date("Ymd", time()) . '/' . $time . '.' . $format;
    $pic_local_url  = $localPath . '/' . date("Ymd", time());
    if (!file_exists($localPath)) {
        mkdir($localPath, 0777,true);
    }
    if (!file_exists($pic_local_url)) {
        mkdir($pic_local_url, 0777,true);
    }
    file_put_contents($pic_local_path, $file);
    return $pic_local_path;
}

/**
 * 解析excel
 * @param $path
 * @param $startI
 * @return array
 */
function getExcelData($path, $startI)
{
    $reader = new Xls();
    try {
        $spreadsheet = $reader->load($path);
    } catch (\Exception $e) {
        $return = [
            'error_code' => 10002,
            'error_msg'  => $e->getMessage(),
            'data'       => []
        ];
        return $return;
    }
    $sheet     = $spreadsheet->getActiveSheet();
    $excelData = array();
    foreach ($sheet->getRowIterator($startI) as $row) {
        $tmp = array();
        foreach ($row->getCellIterator() as $cell) {
            $tmp[] = $cell->getFormattedValue();
        }
        $excelData[$row->getRowIndex()] = $tmp;
    }
    return [
        'error_code' => 0,
        'error_msg'  => '',
        'data'       => $excelData
    ];
}
