<?php
declare (strict_types = 1);

namespace app\service;

use app\service\es\Es;
use think\facade\Db;
use think\facade\Log;

class ShortMessageService
{
    public function callback($param)
    {
        Log::info("钉钉审批回调参数：".json_encode($param,JSON_UNESCAPED_UNICODE));

        if (isset($param['process_instance']['result'], $param['process_instance']['status'], $param['process_instance']['form_component_values']) && is_array($param['process_instance']['form_component_values'])) {
            $result = trim($param['process_instance']['result']);
            $status = trim($param['process_instance']['status']);
            $form_component_values = $param['process_instance']['form_component_values'];
            if ($status === "TERMINATED" || $status === "CANCELED" || ($status === "COMPLETED" && $result === "refuse")) {
                //终止/取消/拒绝状态
                $examinStatus = 2;
            } elseif ($status === "COMPLETED" && $result === "agree") {
                $examinStatus = 1;
            } else {
                $examinStatus = 0; //0为还未结束状态 不用处理  1为失败状态    2为成功状态
            }

        }
        $message_id = '';
        #获取短信群发id
        foreach($form_component_values as $value){
            if($value['name'] == '短信ID'){
                $message_id = $value['value'];
                break;
            }
        }
        #根据群发的发放对象 1=所有用户 2 指定用户 3 导入用户
        #发送对象为 指定用户时  根据筛选 查询数据用户 为所有用户时 查询所有用户 返回redisKey 以及总条数 保存key 总条数
        $smsInfo = Db::name('messages')->where([['id', '=', $message_id]])->find();

//        $smsInfo 的打印值如下:
//            Array
//            (
//                [id] => 199
//                [sms_category] => 2
//                [sms_platform] => 2
//                [sms_scene] => 0
//                [sms_creator] =>
//                [sms_type] => 1
//                [sms_template] => 1
//                [sms_content] => 测试短信https://www.baidu.comaaa短信内容
//                [sms_count] => 3
//                [sms_status] => 1
//                [sms_created] => 2025-07-24 09:43:08
//                [sms_schedule] => 2025-07-24 00:00:00
//                [sms_task_id] =>
//                [sms_phonestr] =>
//                [send_type] => 1
//                [search_criteria] =>
//                {"period":"80884","is_ts":"","province_contain":1,"province_id":"","product_type_contain":1,"product_type":"","send_type":1}
//                [remark] =>
//                [sms_url] => https://www.baidu.com
//                [sms_url_days] => 10
//                )
//        print_r($smsInfo);die ;

        if (empty($smsInfo)) {
            Log::error("未查询到群发短信相关信息 id:".$message_id);
        }
        $task_url = env('ITEM.SMS_URL').'/sms/v3/group/send.html';
        $update['sms_status'] = $examinStatus==1?2:3;
        if($examinStatus == 1){
            //审批通过
            if ($smsInfo['send_type'] == 1 || $smsInfo['send_type'] == 2){ #当是 所有用户 与指定用户时 在队列里面执行查询 发送这一系列操作
                $searchData = json_decode($smsInfo['search_criteria'],true);
                $searchData['send_type'] = $smsInfo['send_type'];
                $searchData['is_total'] = 1;
                $searchData['message_id'] = $message_id;
                #查询筛选数据
                $res= $this->getOrderSendSmsPhoneTotal($searchData);
                Log::info('条件筛选搜索结果：'.json_encode($res));
                if (!$res || !isset($res['total'])){
                    Db::name('messages')->where([['id', '=', $message_id]])->update(['sms_count'=>0,'sms_status'=>6,'remark'=>'筛选数据过大，无法筛选万结果：'.json_encode($res)]);
                    Log::error('条件筛选搜索失败：'.json_encode($res));
                    return false;
                }
                if (empty($res['total']) || $res['total'] == 0){
                    Db::name('messages')->where([['id', '=', $message_id]])->update(['sms_count'=>0,'sms_status'=>5,'remark'=>"未查询到发送数据"]);
                    Log::error('发送数据：'.json_encode($res['total']));
                    return false;
                }
                $update['sms_count'] = isset($res['total'])?$res['total']:0;
                $size = 9999;//每次发送短信数量
                $sms_schedule = $smsInfo['sms_schedule'];
                $sms_schedule = strtotime($sms_schedule); #发送时间
                $nowTime = time()+60*5;//当前时间五分钟后
                //任务参数
                $task_data['type'] = $smsInfo['sms_platform'];
                $task_data['message_id'] = $smsInfo['id'];
                $task_data['star'] = 0;
                $task_data['end'] = $size;
                $task_data['sms_count'] = $res['total'];
                $task_data['sms_type'] = $smsInfo['sms_type'];
                $task_json = json_encode($task_data);
                $sms_schedule = $nowTime < $sms_schedule?$sms_schedule:$nowTime;
                $update['sms_task_id'] = timedTaskadd($sms_schedule, $task_url,$task_json);
                $update['sms_schedule'] = $nowTime < $sms_schedule?$smsInfo['sms_schedule']:date('Y-m-d H:i:s', $sms_schedule);
                $update['sms_phonestr'] =  "vinehoo.sms.orderuids.".$message_id;
            }else{
                $sms_count = $smsInfo['sms_count'];//总条数
                //审批通过
                $smsInfo = Db::name('messages')->where([['id', '=', $message_id]])->find();
                $size = 999;//每次发送短信数量
                $sms_schedule = $smsInfo['sms_schedule'];
                $sms_schedule = strtotime($sms_schedule);
                $nowTime = time()+60*5;//当前时间五分钟后
                //原计划进行,推送计划任务,分页发送
                $task_data['type'] = $smsInfo['sms_platform'];
                $task_data['message_id'] = $smsInfo['id'];
                $task_data['star'] = 0;
                $task_data['end'] = $size;
                $task_data['sms_count'] = $sms_count;
                $task_data['sms_type'] = $smsInfo['sms_type'];
                $task_json = json_encode($task_data);
                $sms_schedule = $nowTime < $sms_schedule?$sms_schedule:$nowTime;
                $update['sms_task_id'] = timedTaskadd($sms_schedule, $task_url,$task_json);
                $update['sms_schedule'] = $nowTime < $sms_schedule?$smsInfo['sms_schedule']:date('Y-m-d H:i:s', $sms_schedule);
            }
        }
        $edit= Db::name('messages')->where([['id', '=', $message_id]])->update($update);
        if (!$edit) {
            $data['error_code'] = 10001;
            $data['error_msg'] = "状态修改失败";
            $data['data'] = [];
            return $data;
        }
        $data['error_code'] = 0;
        $data['error_msg'] = 'OK';
        $data['data'] = [];
        $data['last_update'] = time();
        return true;
    }

    /**
     * zjl 发送短信 条件筛选
     * @param $params
     * @return array|\think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOrderSendSmsPhone($params)
    {
        ini_set('memory_limit','512M');
        #指定用户
        $user = [];
        $count = 0;
        $page = isset($params['page'])?$params['page']:1;
        $limit = isset($params['limit'])?$params['limit']:10000;
        $offset=($page-1)*$limit;
        if (!isset($params['is_total'])) {
            $params['is_total'] = 0;
        }
        if ($params['send_type'] == 1) {
            $where        = [];
            # 商品类型
            $periodsId = [];
            if (isset($params['product_type']) && !empty($params['product_type'])) {
                $productType = explode(',', $params['product_type']);
                $productWhere = [];
                #产品类型包含/不包含

                if ($params['product_type_contain'] == 2) {
                    $productWhere[] = ['product_category', 'not in', $productType];
                } else {
                    $productWhere[] = ['product_category', 'in', $productType];
                }
                if (isset($params['period']) && !empty($params['period'])) {
                    $productWhere[] = ['id', '=', $params['period']];
                }
                $productWhere[] = ['onsale_status', '=', 2];
                $productWhere[] = ['id', '<>', null];
                $list = Es::name('periods')
                    ->field('id,product_category')
                    ->where($productWhere)
                    ->select()->toArray();
                if (count($list) > 0) {
                    foreach ($list as $v) {
                        if (!empty($v) && isset($v['id'])) {
                            $periodsId[] = $v['id'];
                        }
                    }
                }

            }
            #下单时间  是否暂存 期数  地区省 包含/不包含 产品类型
            if (isset($params['stime']) && !empty($params['stime'])) {
                $where[] = ['created_time','>',strtotime($params['stime'])];
                $where[] = ['created_time','<=',strtotime($params['etime'])];


            }
            if (isset($params['period']) && !empty($params['period'])) {
                if ((!isset($params['product_type']) || empty($params['product_type'])) && empty($provinceId)) {
                    $periodsId = explode(',', $params['period']);
                }
            }
            if (!empty($periodsId)) {
                $where[] = ['period','in', array_unique($periodsId)];
            }
            if (isset($params['is_ts']) && is_numeric($params['is_ts'])) {
                $where[] = ['is_ts','=',intval($params['is_ts'])];
            }
            $userId = [];
            $where[] = ['sub_order_status','in',[1,2,3]];
            #数据库查询 订单类型：0-闪购 1-秒发 2-跨境 3-尾货（优化：使用GROUP BY去重）
            try {
                $db = Db::connect('vh_order');

                // 优化：使用GROUP BY uid在数据库层面去重，减少数据传输和内存占用
                $flashOrderUid =  $db->name('flash_order')->where($where)->group('uid')->order('id','desc')->limit(0,30000)->column("uid");
                $secondOrderUid =  $db->name('second_order')->where($where)->group('uid')->order('id','desc')->limit(0,30000)->column("uid");
                $crossOrderUid =  $db->name('cross_order')->where($where)->group('uid')->order('id','desc')->limit(0,30000)->column("uid");
                $tailOrderUid = $db->name('tail_order')->where($where)->group('uid')->order('id','desc')->limit(0,30000)->column("uid");

                // 合并所有uid并最终去重
                $userId = array_merge($userId,$flashOrderUid,$secondOrderUid,$crossOrderUid,$tailOrderUid);
                $userId = array_unique($userId);

                Log::info("优化后uid个数：".count($userId));
            }catch (\Exception $e) {
                Log::info("订单查询失败：".$e->getMessage());
                return ['telephone'=>[],'total'=>0];
            }

            Log::info("uid个数：".count($userId));
            #用户收货地区查询省 包含/不包含
            if (isset($params['province_id']) && !empty($params['province_id'])) {
                $provinceId = explode(',', strval($params['province_id']));
                $addressWhere = [];
                #默认包含
                if ($params['province_contain'] == 2) {
                    $addressWhere[] = ['province_id', 'not in', $provinceId];
                } else {
                    $addressWhere[] = ['province_id', 'in', $provinceId];
                }
                $addressWhere[] = [['is_default', '=', 1], ['is_delete', '=', 0]];
                #当搜索条件只有地区时  不需要判断$userId 是否为空 当包含多个条件时 $userId为空不查询
                if (!empty($params['period']) || !empty($params['product_type'])  || !empty($params['stime'])  || !empty($params['etime']) || is_numeric($params['is_ts'])) {
                    $userAddress = [];
                    if (!empty($userId)) {
                        $addressWhere[] = ['uid', 'in', array_unique($userId)];
                        $userAddress = Db::connect('vh_user')->name("user_address")->field('id,uid')->where($addressWhere)->select()->toArray();
                    }
                } else {
                    $userAddress = Db::connect('vh_user')->name("user_address")->field('id,uid')->where($addressWhere)->select()->toArray();
                }
                $userids = array_unique(array_column($userAddress, 'uid'));
                $userId = array_merge($userId, $userids);
            }
            #当搜索条件只有注册时间  不需要判断$userId 是否为空 当包含多个条件时 $userId为空不查询
            if (!empty($params['period']) || !empty($params['product_type'])  || !empty($params['stime'])  || !empty($params['etime']) || is_numeric($params['is_ts']) || !empty($params['province_id'])) {
                $user = [];
                if (!empty($userId)) {
                    // 优化：当用户ID数量过多时，分批查询避免SQL占位符限制
                    $uniqueUserIds = array_unique($userId);
                    Log::info("准备查询用户信息，用户ID数量：" . count($uniqueUserIds));

                    if (count($uniqueUserIds) > 50000) {
                        // 分批查询用户信息
                        $user = $this->getUserInfoInBatches($uniqueUserIds, $params, $offset, $limit);
                        if ($params['is_total'] == 1) {
                            $count = count($user);
                        }
                    } else {
                        // 正常查询
                        $userWhere = [];
                        $userWhere[] = ['uid', 'in', $uniqueUserIds];
                        if (isset($params['create_stime']) && isset($params['create_etime']) && !empty($params['create_stime']) && !empty($params['create_etime'])) {
                            $userWhere[] = ['created_time', 'between', [strtotime($params['create_stime']), strtotime($params['create_etime'])]];
                        }
                        $userWhere[] = [['is_delete','=','0'],['is_disabled','=',1],['telephone','<>','']];
                        if ($params['is_total'] == 1) {
                            $count = Db::connect('vh_user')->name("user")->field('uid')->where($userWhere)->count();
                        }else{
                            $user = Db::connect('vh_user')->name('user')->field("uid,telephone")
                                ->where($userWhere)
                                ->limit(intval($offset),intval($limit))
                                ->select()->toArray();
                        }
                    }
                }
            } else {
                if (isset($params['create_stime']) && isset($params['create_etime']) && !empty($params['create_stime']) && !empty($params['create_etime'])) {
                    $userWhere[] = ['created_time', 'between', [strtotime($params['create_stime']), strtotime($params['create_etime'])]];
                }
                $userWhere[] = [['is_delete','=','0'],['is_disabled','=',1],['telephone','<>','']];
                if ($params['is_total'] == 1) {
                    $count =Db::connect('vh_user')->name('user')->field("uid")->where($userWhere)->count();
                }else{
                    $user = Db::connect('vh_user')->name('user')->field("uid,telephone")->where($userWhere)
                        ->limit(intval($offset),intval($limit))
                        ->select()->toArray();
                }
            }
        }
        if ($params['send_type'] == 2) {  # 所有用户
            $where [] =[['is_delete','=','0'],['is_disabled','=',1],['telephone','<>','']];
            if ($params['is_total'] == 1) {
                $count = Db::name('user')->field("uid")->where($where)->count('uid');
            }else{
                $user = Db::name('user')->field("uid,telephone")->where($where)->limit(intval($offset),intval($limit))->select()->toArray();
            }
        }
        if ($params['is_total'] == 1) {
            Log::info("筛选条数：".$count);
            return ['telephone'=>[],'total'=>$count];
        }else{
            $telephone = array_unique(array_column($user,'telephone'));
            Log::info("查询到加密手机号数量：" . count($telephone));

            #手机号分批解密（优化大数据量处理）
            $telephone = $this->decryptPhonesInBatches($telephone, 1000);
            return ['telephone'=>$telephone,'total'=>$count];
        }
    }

    /**
     * 分批解密手机号（优化大数据量处理）
     * @param array $phones 待解密的手机号数组
     * @param int $batchSize 每批解密数量，默认1000条
     * @return array 解密后的手机号数组
     */
    private function decryptPhonesInBatches($phones, $batchSize = 1000)
    {
        if (empty($phones)) {
            return [];
        }

        $decryptedPhones = [];
        $batches         = array_chunk($phones, $batchSize);
        $totalBatches    = count($batches);

        Log::info("开始分批解密，总数量：" . count($phones) . "，分批数：{$totalBatches}");

        foreach ($batches as $index => $batch) {
            try {
                $batchNum = $index + 1;
                Log::info("解密第 {$batchNum}/{$totalBatches} 批手机号，数量：" . count($batch));

                $decrypted = cryptionDeal([
                    'orig_data' => $batch,
                    'uid'       => 44,
                    'operator'  => '短信发送'
                ], 'D');

                if (is_array($decrypted)) {
                    $decryptedPhones = array_merge($decryptedPhones, $decrypted);
                    Log::info("第 {$batchNum} 批解密成功，本批解密：" . count($decrypted) . "条");
                } else {
                    Log::error("第 {$batchNum} 批解密返回格式错误");
                }

                // 解密服务调用间隔，避免频率过高
                if ($index < $totalBatches - 1) {
                    usleep(100000); // 0.1秒延迟
                }

            } catch (\Exception $e) {
                Log::error("解密第 " . ($index + 1) . " 批手机号失败：" . $e->getMessage());
                // 解密失败时，跳过这批数据，继续处理下一批
                continue;
            }
        }

        Log::info("分批解密完成，最终解密数量：" . count($decryptedPhones));
        return $decryptedPhones;
    }

    /**
     * 分批查询用户信息（避免SQL占位符限制）
     * @param array $userIds 用户ID数组
     * @param array $params 查询参数
     * @param int $offset 偏移量
     * @param int $limit 限制数量
     * @return array 用户信息数组
     */
    private function getUserInfoInBatches($userIds, $params, $offset, $limit)
    {
        $batchSize = 30000; // 每批最多3万个ID，避免SQL占位符限制
        $allUsers = [];
        $batches = array_chunk($userIds, $batchSize);
        $totalBatches = count($batches);

        Log::info("用户ID数量过多，启用分批查询，分批数：{$totalBatches}");

        foreach ($batches as $index => $batchUserIds) {
            try {
                $batchNum = $index + 1;
                Log::info("查询第 {$batchNum}/{$totalBatches} 批用户信息，ID数量：" . count($batchUserIds));

                $userWhere = [];
                $userWhere[] = ['uid', 'in', $batchUserIds];
                if (isset($params['create_stime']) && isset($params['create_etime']) && !empty($params['create_stime']) && !empty($params['create_etime'])) {
                    $userWhere[] = ['created_time', 'between', [strtotime($params['create_stime']), strtotime($params['create_etime'])]];
                }
                $userWhere[] = [['is_delete','=','0'],['is_disabled','=',1],['telephone','<>','']];

                $batchUsers = Db::connect('vh_user')->name('user')
                    ->field("uid,telephone")
                    ->where($userWhere)
                    ->select()->toArray();

                $allUsers = array_merge($allUsers, $batchUsers);
                Log::info("第 {$batchNum} 批查询完成，本批用户数：" . count($batchUsers) . "，累计：" . count($allUsers));

            } catch (\Exception $e) {
                Log::error("查询第 " . ($index + 1) . " 批用户信息失败：" . $e->getMessage());
                continue;
            }
        }

        // 如果需要分页，在这里处理
        if ($limit > 0 && $offset >= 0) {
            $allUsers = array_slice($allUsers, $offset, $limit);
        }

        Log::info("分批查询用户信息完成，最终用户数：" . count($allUsers));
        return $allUsers;
    }

    /**
     * 获取用户信息
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getUserPhone($params)
    {
        ini_set('memory_limit','512M');
        #指定用户
        $user = [];
        $count = 0;
        $page = isset($params['page'])?$params['page']:1;
        $limit = isset($params['limit'])?$params['limit']:9999;
        $offset=($page-1)*$limit;
        if ($params['send_type'] == 1) {
            $where        = [];
            $userId=[];
            $redis = new \Redis();
            $host = env('CACHE.HOST');
            $port = env('CACHE.PORT');
            $password = env('CACHE.PASSWORD');
            $redis->connect($host,intval($port));
            $redis->auth($password);
            $redis->select(14);
            $sms_phonestr = "vinehoo.sms.orderuids.".$params['message_id'];
            $userIds = $redis->get($sms_phonestr);
            $userId = explode(",",$userIds);
            Log::info("redi取出uid个数：".count($userId));
            #用户收货地区查询省 包含/不包含
            if (isset($params['province_id']) && !empty($params['province_id'])) {
                $provinceId = explode(',', $params['province_id']);
                $addressWhere = [];
                #默认包含
                if ($params['province_contain'] == 2) {
                    $addressWhere[] = ['province_id', 'not in', $provinceId];
                } else {
                    $addressWhere[] = ['province_id', 'in', $provinceId];
                }
                $addressWhere[] = [['is_default', '=', 1], ['is_delete', '=', 0]];
                #当搜索条件只有地区时  不需要判断$userId 是否为空 当包含多个条件时 $userId为空不查询
                if (!empty($params['period']) || !empty($params['product_type'])  || !empty($params['stime'])  || !empty($params['etime']) || is_numeric($params['is_ts'])) {
                    $userAddress = [];
                    if (!empty($userId)) {
                        $addressWhere[] = ['uid', 'in', array_unique($userId)];
                        $userAddress = Db::connect('vh_user')->name("user_address")->field('id,uid')->where($addressWhere)->select()->toArray();
                    }
                } else {
                    $userAddress = Db::connect('vh_user')->name("user_address")->field('id,uid')->where($addressWhere)->select()->toArray();
                }
                $userids = array_unique(array_column($userAddress, 'uid'));
                $userId = array_merge($userId, $userids);
            }
            #当搜索条件只有注册时间  不需要判断$userId 是否为空 当包含多个条件时 $userId为空不查询
            if (!empty($params['period']) || !empty($params['product_type'])  || !empty($params['stime'])  || !empty($params['etime']) || is_numeric($params['is_ts']) || !empty($params['province_id'])) {
                $user = [];
                Log::info("uid个数：".count($userId));
                if (!empty($userId)) {
                    $userWhere = [];
                    $userWhere[] = ['uid', 'in', array_unique($userId)];
                    if (isset($params['create_stime']) && isset($params['create_etime']) && !empty($params['create_stime']) && !empty($params['create_etime'])) {
                        $userWhere[] = ['created_time', 'between', [strtotime($params['create_stime']), strtotime($params['create_etime'])]];
                    }
                    $userWhere[] = [['is_delete','=','0'],['is_disabled','=',1],['telephone','<>','']];
                    $user = Db::connect('vh_user')->name('user')->field("uid,telephone")
                        ->where($userWhere)
                        ->limit(intval($offset),intval($limit))
                        ->select()->toArray();
                }
            } else {
                if (isset($params['create_stime']) && isset($params['create_etime']) && !empty($params['create_stime']) && !empty($params['create_etime'])) {
                    $userWhere[] = ['created_time', 'between', [strtotime($params['create_stime']), strtotime($params['create_etime'])]];
                }
                $userWhere[] = [['is_delete','=','0'],['is_disabled','=',1],['telephone','<>','']];
                $user = Db::connect('vh_user')->name('user')->field("uid,telephone")->where($userWhere)
                    ->limit(intval($offset),intval($limit))
                    ->select()->toArray();
            }
        }
        if ($params['send_type'] == 2) {  # 所有用户
            $where [] =[['is_delete','=','0'],['is_disabled','=',1],['telephone','<>','']];
            $user = Db::name('user')->field("uid,telephone")->where($where)->limit(intval($offset),intval($limit))->select()->toArray();

        }
        $telephone = array_unique(array_column($user,'telephone'));
        #手机号解密
        $encrypt   = cryptionDeal([
            'orig_data' => $telephone,
            'uid'       => 44,
            'operator'  => '短信发送'
        ], 'D');
        return ['telephone'=>$encrypt,'total'=>0];
    }

    /**
     * 获取总条数
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOrderSendSmsPhoneTotal($params,$saveCache = true)
    {
        ini_set('memory_limit','215M');
        #指定用户
        $user = [];
        $count = 0;
        $page = isset($params['page'])?$params['page']:1;
        $limit = isset($params['limit'])?$params['limit']:9999;
        $offset=($page-1)*$limit;
        if (!isset($params['is_total'])) {
            $params['is_total'] = 0;
        }

        if ($params['send_type'] == 1) {
            $where        = [];
            # 商品类型
            $periodsId = [];
            if (isset($params['product_type']) && !empty($params['product_type'])) {
                $productType = explode(',', $params['product_type']);
                $productWhere = [];
                #产品类型包含/不包含

                if ($params['product_type_contain'] == 2) {
                    $productWhere[] = ['product_category', 'not in', $productType];
                } else {
                    $productWhere[] = ['product_category', 'in', $productType];
                }
                if (isset($params['period']) && !empty($params['period'])) {
                    $period = explode(',', strval($params['period']));
                    $productWhere[] = ['id', 'in', $period];
                }
                $productWhere[] = ['onsale_status', '=', 2];
                $productWhere[] = ['id', '<>', null];
                $list = Es::name('periods')
                    ->field('id,product_category')
                    ->where($productWhere)
                    ->select()->toArray();
                if (count($list) > 0) {
                    foreach ($list as $v) {
                        if (!empty($v) && isset($v['id'])) {
                            $periodsId[] = $v['id'];
                        }
                    }
                }

            }
            #下单时间  是否暂存 期数  地区省 包含/不包含 产品类型
            if (isset($params['stime']) && !empty($params['stime'])) {
                $where[] = ['created_time','>',strtotime($params['stime'])];
                $where[] = ['created_time','<=',strtotime($params['etime'])];
            }
            if (isset($params['period']) && !empty($params['period'])) {
                if ((!isset($params['product_type']) || empty($params['product_type'])) && empty($provinceId)) {
                    $periodsId = explode(',', $params['period']);
                }
            }
            if (!empty($periodsId)) {
                $where[] = ['period','in', array_unique($periodsId)];
            }
            if (isset($params['is_ts']) && is_numeric($params['is_ts'])) {
                $where[] = ['is_ts','=',intval($params['is_ts'])];
            }
            #用户收货地区查询省 包含/不包含
            if (isset($params['province_id']) && !empty($params['province_id'])) {
                $provinceId = explode(',', strval($params['province_id']));
                $addressWhere = [];
                #默认包含
                if ($params['province_contain'] == 2) {
                    $addressWhere[] = ['province_id', 'not in', $provinceId];
                } else {
                    $addressWhere[] = ['province_id', 'in', $provinceId];
                }
              //  $addressWhere[] = [['is_default', '=', 1], ['is_delete', '=', 0]];
              //  $userAddress = Db::connect('vh_user')->name("user_address")->field('id,uid')->where($addressWhere)->select()->toArray();
                $userAddress =  Db::connect('vh_order')->name('order_main')->field("uid")->where([['main_order_status','in',[1,2,3]],['uid','>',0]])->where($addressWhere)->group("uid")->select()->toArray();
                $userAddressId = array_unique(array_column($userAddress, 'uid'));
                $userAddressId = array_values($userAddressId);
                $where[]= ['uid',"in",$userAddressId];

            }


        if (!empty($where)){
              try {
                    $userId = [];
                $where[] = ['sub_order_status','in',[1,2,3]];
                #数据库查询 订单类型：0-闪购 1-秒发 2-跨境 3-尾货
                $db = Db::connect('vh_order');
                $flashOrder =  $db->name('flash_order')->field("uid")->where($where)->group("uid")->select()->toArray();
                $flashOrderUid = array_column($flashOrder,'uid');
                $secondOrder =  $db->name('second_order')->field("uid")->where($where)->group("uid")->select()->toArray();
                $secondOrderUid = array_column($secondOrder,'uid');
                $crossOrder =  $db->name('cross_order')->field("uid")->where($where)->group("uid")->select()->toArray();
                $crossOrderUid = array_column($crossOrder,'uid');
                $tailOrder = $db->name('tail_order')->field("uid")->where($where)->group("uid")->select()->toArray();
                $tailOrderUid = array_column($tailOrder,'uid');
                $userId = array_merge($userId,$flashOrderUid,$secondOrderUid,$crossOrderUid,$tailOrderUid);
                $userId = array_unique($userId);
                Log::info("uid个数：".count($userId));
                if($saveCache){
                    $redis = new \Redis();
                    $host = env('CACHE.HOST');
                    $port = env('CACHE.PORT');
                    $password = env('CACHE.PASSWORD');
                    $redis->connect($host,intval($port));
                    $redis->auth($password);
                    $redis->select(14);
                    $sms_phonestr = "vinehoo.sms.orderuids.".$params['message_id'];
                    $redis->del($sms_phonestr);
                    $size =  ceil(intval(count($userId))/$limit);
                    for($i = 1; $i <= $size; $i++) {
                        $offset=($i-1)*$limit;
                        $userIdArr = array_slice($userId, intval($offset), intval($limit));
                        $redis->rPush($sms_phonestr,implode(",",$userIdArr));
                    }
                }
                }catch (\Exception $e) {
                    Log::info("订单查询失败：".$e->getMessage());
                    return ['telephone'=>[],'total'=>0];
                }
            }

               // $redis->set($sms_phonestr,implode(",",$userId));
            #用户收货地区查询省 包含/不包含
//            if (isset($params['province_id']) && !empty($params['province_id'])) {
//                $provinceId = explode(',', strval($params['province_id']));
//                $addressWhere = [];
//                #默认包含
//                if ($params['province_contain'] == 2) {
//                    $addressWhere[] = ['province_id', 'not in', $provinceId];
//                } else {
//                    $addressWhere[] = ['province_id', 'in', $provinceId];
//                }
//                $addressWhere[] = [['is_default', '=', 1], ['is_delete', '=', 0]];
//                #当搜索条件只有地区时  不需要判断$userId 是否为空 当包含多个条件时 $userId为空不查询
//                if (!empty($params['period']) || !empty($params['product_type'])  || !empty($params['stime'])  || !empty($params['etime']) || is_numeric($params['is_ts'])) {
//                    $userAddress = [];
//                    if (!empty($userId)) {
//                        $addressWhere[] = ['uid', 'in', array_unique($userId)];
//                        $userAddress = Db::connect('vh_user')->name("user_address")->field('id,uid')->where($addressWhere)->select()->toArray();
//                    }
//                } else {
//                    $userAddress = Db::connect('vh_user')->name("user_address")->field('id,uid')->where($addressWhere)->select()->toArray();
//                }
//                $userids = array_unique(array_column($userAddress, 'uid'));
//                $userId = array_merge($userId, $userids);
//            }
            #当搜索条件只有注册时间  不需要判断$userId 是否为空 当包含多个条件时 $userId为空不查询
            if (!empty($params['period']) || !empty($params['product_type'])  || !empty($params['stime'])  || !empty($params['etime']) || is_numeric($params['is_ts']) || !empty($params['province_id'])) {
                $user = [];
                if (!empty($userId)) {
                    $userWhere = [];
                    $userWhere[] = ['uid', 'in', array_unique($userId)];
                    if (isset($params['create_stime']) && isset($params['create_etime']) && !empty($params['create_stime']) && !empty($params['create_etime'])) {
                        $userWhere[] = ['created_time', 'between', [strtotime($params['create_stime']), strtotime($params['create_etime'])]];
                    }
                    $userWhere[] = [['is_delete','=','0'],['is_disabled','=',1],['telephone','<>','']];
                    //$u_ids = Db::connect('vh_user')->name("user")->where($userWhere)->column('uid');

                    $result = Db::connect('vh_user')->name("user")->whereRaw("telephone <> '' and (`is_delete` = 1 OR `is_disabled` > 1)")->column('uid');
                    $usersToRemove = array_flip($result);
                    $allUsers = array_unique($userId);
                    $u_ids = [];
                    foreach ($allUsers as $id) {  // O(n)
                        if (!isset($usersToRemove[$id])) {
                            $u_ids[] = $id;
                        }
                    }

                    $count = count($u_ids);
                    if($saveCache){
                        $u_ids_g = array_chunk($u_ids, $limit);
                        $redis = new \Redis();
                        $host = env('CACHE.HOST');
                        $port = env('CACHE.PORT');
                        $password = env('CACHE.PASSWORD');
                        $redis->connect($host, intval($port));
                        $redis->auth($password);
                        $redis->select(14);
                        $sms_phonestr = "vinehoo.sms.orderuids." . $params['message_id'];
                        $redis->del($sms_phonestr);
                        foreach ($u_ids_g as $uig) {
                            $redis->rPush($sms_phonestr, implode(",", $uig));
                        }
                    }
                }
            } else {
                if (isset($params['create_stime']) && isset($params['create_etime']) && !empty($params['create_stime']) && !empty($params['create_etime'])) {
                    $userWhere[] = ['created_time', 'between', [strtotime($params['create_stime']), strtotime($params['create_etime'])]];
                }
                $userWhere[] = [['is_delete','=','0'],['is_disabled','=',1],['telephone','<>','']];
                $u_ids = Db::connect('vh_user')->name("user")->where($userWhere)->column('uid');
                $count = count($u_ids);
                if($saveCache){
                    $u_ids_g = array_chunk($u_ids, $limit);
                    $redis = new \Redis();
                    $host = env('CACHE.HOST');
                    $port = env('CACHE.PORT');
                    $password = env('CACHE.PASSWORD');
                    $redis->connect($host, intval($port));
                    $redis->auth($password);
                    $redis->select(14);
                    $sms_phonestr = "vinehoo.sms.orderuids." . $params['message_id'];
                    $redis->del($sms_phonestr);
                    foreach ($u_ids_g as $uig) {
                        $redis->rPush($sms_phonestr, implode(",", $uig));
                    }
                }
            }
        }

        if ($params['send_type'] == 2) {  # 所有用户
            $where [] =[['is_delete','=','0'],['is_disabled','=',1],['telephone','<>','']];
            $count = Db::connect('vh_user')->name('user')->field("uid")->where($where)->count('uid');
        }
        Log::info("筛选条数：".$count);
        return ['telephone'=>[],'total'=>$count];
    }

    public function getUserPhoneToRedis($params)
    {
        ini_set('memory_limit','512M');
        #指定用户
        $user = [];
        $page = isset($params['page'])?$params['page']:1;
        $limit = isset($params['limit'])?$params['limit']:9999;
        $offset=($page-1)*$limit;
        if ($params['send_type'] == 1) {
            $where        = [];
            $userId=[];
            $redis = new \Redis();
            $host = env('CACHE.HOST');
            $port = env('CACHE.PORT');
            $password = env('CACHE.PASSWORD');
            $redis->connect($host,intval($port));
            $redis->auth($password);
            $redis->select(14);
            $sms_phonestr = "vinehoo.sms.orderuids.".$params['message_id'];
            $userIds = $redis->get($sms_phonestr);
            $userId = explode(",",$userIds);
            #用户收货地区查询省 包含/不包含
            if (isset($params['province_id']) && !empty($params['province_id'])) {
                $provinceId = explode(',', $params['province_id']);
                $addressWhere = [];
                #默认包含
                if ($params['province_contain'] == 2) {
                    $addressWhere[] = ['province_id', 'not in', $provinceId];
                } else {
                    $addressWhere[] = ['province_id', 'in', $provinceId];
                }
                $addressWhere[] = [['is_default', '=', 1], ['is_delete', '=', 0]];
                #当搜索条件只有地区时  不需要判断$userId 是否为空 当包含多个条件时 $userId为空不查询
                if (!empty($params['period']) || !empty($params['product_type'])  || !empty($params['stime'])  || !empty($params['etime']) || is_numeric($params['is_ts'])) {
                    $userAddress = [];
                    if (!empty($userId)) {
                        $addressWhere[] = ['uid', 'in', array_unique($userId)];
                        $userAddress = Db::connect('vh_user')->name("user_address")->field('id,uid')->where($addressWhere)->select()->toArray();
                    }
                } else {
                    $userAddress = Db::connect('vh_user')->name("user_address")->field('id,uid')->where($addressWhere)->select()->toArray();
                }
                $userids = array_unique(array_column($userAddress, 'uid'));
                $userId = array_merge($userId, $userids);
            }
            #当搜索条件只有注册时间  不需要判断$userId 是否为空 当包含多个条件时 $userId为空不查询
            if (!empty($params['period']) || !empty($params['product_type'])  || !empty($params['stime'])  || !empty($params['etime']) || is_numeric($params['is_ts']) || !empty($params['province_id'])) {
                $user = [];
                Log::info("uid个数：".count($userId));
                if (!empty($userId)) {
                    $userWhere = [];
                    $userWhere[] = ['uid', 'in', array_unique($userId)];
                    if (isset($params['create_stime']) && isset($params['create_etime']) && !empty($params['create_stime']) && !empty($params['create_etime'])) {
                        $userWhere[] = ['created_time', 'between', [strtotime($params['create_stime']), strtotime($params['create_etime'])]];
                    }
                    $userWhere[] = [['is_delete','=','0'],['is_disabled','=',1],['telephone','<>','']];
                    $user = Db::connect('vh_user')->name('user')->field("uid,telephone")
                        ->where($userWhere)
                        ->limit(intval($offset),intval($limit))
                        ->select()->toArray();
                }else{
                    if (isset($params['create_stime']) && isset($params['create_etime']) && !empty($params['create_stime']) && !empty($params['create_etime'])) {
                        $userWhere[] = ['created_time', 'between', [strtotime($params['create_stime']), strtotime($params['create_etime'])]];
                    }
                    $userWhere[] = [['is_delete','=','0'],['is_disabled','=',1],['telephone','<>','']];
                    $user = Db::connect('vh_user')->name('user')->field("uid,telephone")
                        ->where($userWhere)
                        ->limit(intval($offset),intval($limit))
                        ->select()->toArray();
                }
            } else {
                if (isset($params['create_stime']) && isset($params['create_etime']) && !empty($params['create_stime']) && !empty($params['create_etime'])) {
                    $userWhere[] = ['created_time', 'between', [strtotime($params['create_stime']), strtotime($params['create_etime'])]];
                }
                $userWhere[] = [['is_delete','=','0'],['is_disabled','=',1],['telephone','<>','']];
                $user = Db::connect('vh_user')->name('user')->field("uid,telephone")->where($userWhere)
                    ->limit(intval($offset),intval($limit))
                    ->select()->toArray();
            }
        }
        if ($params['send_type'] == 2) {  # 所有用户
            $where [] =[['is_delete','=','0'],['is_disabled','=',1],['telephone','<>','']];
            $user = Db::name('user')->field("uid,telephone")->where($where)->limit(intval($offset),intval($limit))->select()->toArray();

        }
        $telephone = array_unique(array_column($user,'telephone'));
        #手机号解密
        $encrypt   = cryptionDeal([
            'orig_data' => $telephone,
            'uid'       => 44,
            'operator'  => '短信发送'
        ], 'D');
        return ['telephone'=>$encrypt,'total'=>0];
    }
    /**
     * 钉钉队列推送
     * @param $data
     * @return array|bool|string
     */
    public function pushQueue($data)
    {
        $queueData=json_encode(['data'=>$data],JSON_UNESCAPED_UNICODE);
        $queueData=base64_encode($queueData);
        $pushData = array(
            'exchange_name' => 'sms',
            'routing_key'   => 'users.filter.sms',
            #推送数据
            'data'=>$queueData,
        );
        $json = json_encode($pushData);
        $res = curlRequest(env('ITEM.QUEUE_URL'),$json);
        $res = json_decode($res,true);
        Log::info("筛选队列推送：".$res);
        return true;
    }
}