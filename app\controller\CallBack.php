<?php


namespace app\controller;


use app\BaseController;
use app\service\ShortMessageService;
use app\service\Sms;
use think\facade\Db;
use think\facade\Log;

class CallBack extends BaseController{
    /**
     * @Describe:定时任务回调发送简单短息(亿美限制500,腾域1000统一500条)
     * <AUTHOR>
     * @Date 2021/9/15 15:22
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function send(){
        $param = $this->request->post();
        Log::info('内网访问成功:'.json_encode($param,JSON_UNESCAPED_UNICODE));
        $type = $param['type'];//1:亿美 2:腾域 // 2
        $sms_type = $param['sms_type']; // 1
        $message_id = $param['message_id']; // 336
        $star = $param['star']-1<=0?0:$param['star'];
        $end = $param['end']-1<0?0:$param['end'];
        $where[] = ['id','=',$message_id];
//        $where[] = ['sms_status','=',2];todo .....
        $smsinfo = Db::name('messages')->where($where)->find();

        if(empty($smsinfo)){
            Log::info('未找到id为$message_id的短信或已经取消发送');
            $data['error_code'] = -1;
            $data['error_msg'] = '未找到短信或已经取消发送';
            $data['data'] = "";
        }
        $content = $smsinfo['sms_content']; #发送内容
        $sms_count = $smsinfo['sms_count']; #发送条数


//        print_r($smsinfo);
//        Array
//        (
//            [id] => 199
//            [sms_category] => 2
//            [sms_platform] => 2
//            [sms_scene] => 0
//            [sms_creator] =>
//            [sms_type] => 1
//            [sms_template] => 1
//            [sms_content] => 测试短信https://www.baidu.comaaa短信内容
//            [sms_count] => 3
//            [sms_status] => 2
//            [sms_created] => 2025-07-24 09:43:08
//            [sms_schedule] => 2025-07-24 10:53:36
//            [sms_task_id] => success
//                    [sms_phonestr] => vinehoo.sms.orderuids.199
//            [send_type] => 1
//            [search_criteria] =>
//            {"period":"80884","is_ts":"","province_contain":1,"province_id":"","product_type_contain":1,"product_type":"","send_type":1}
//            [remark] =>
//            [sms_url] => https://www.baidu.com
//            [sms_url_days] => 10
//            )


        // 判断是短地址短信，直接发送，不请求GO服务
        if (!empty($smsinfo['sms_url'])) {
            return $this->sendShortUrlSms($smsinfo, $param);
        }

        if ($smsinfo['send_type'] == 1 || $smsinfo['send_type'] == 2) {

            $searchData = json_decode($smsinfo['search_criteria'], true);
            $searchData['send_type'] = $smsinfo['send_type'];
            $pushData = [
                "send_type"=>intval($smsinfo['send_type']),
                "message_id"=>intval($param['message_id']),
                "total"=>intval($smsinfo['sms_count']),
                "sms_category"=>intval($smsinfo['sms_category']),
                "create_stime"=>isset($searchData['create_stime'])?strtotime($searchData['create_stime']):0,
                "create_etime"=>isset($searchData['create_etime'])? strtotime($searchData['create_etime']):0,
                "sms_content"=>$smsinfo['sms_content'],
                "sms_phonestr"=>isset($smsinfo['sms_phonestr'])?$smsinfo['sms_phonestr']:'',
            ];
            Log::info("Go短信发送请求数据:".json_encode($pushData));
            $data = json_encode($pushData,JSON_UNESCAPED_UNICODE);
            $url = env("ITEM.MARKINGKET_SMS_SEND_URL")."/sms/v3/sms/sendSms";
            http_post_json($url,$data);
            Db::name('messages')->where($where)->update(['sms_status'=>4]);
            $result['error_code'] = 0;
            $result['error_msg'] = '已发送';
            $result['result'] = [];
            return json($result);
        }else{

            $pushData = [
                "send_type"=>intval($smsinfo['send_type']),
                "message_id"=>intval($param['message_id']),
                "total"=>intval($smsinfo['sms_count']),
                "sms_category"=>intval($smsinfo['sms_category']),
                "create_stime"=>0,
                "create_etime"=>0,
                "sms_content"=>$smsinfo['sms_content'],
                "sms_phonestr"=>isset($smsinfo['sms_phonestr'])?$smsinfo['sms_phonestr']:'',
            ];
            Log::info("Go短信发送请求数据:".json_encode($pushData));
            $data = json_encode($pushData,JSON_UNESCAPED_UNICODE);
            $url = env("ITEM.MARKINGKET_SMS_SEND_URL")."/sms/v3/sms/sendSms";
            http_post_json($url,$data);
            Db::name('messages')->where($where)->update(['sms_status'=>4]);
            $result['error_code'] = 0;
            $result['error_msg'] = '已发送';
            $result['result'] = [];
            return json($result);
//            $redis = new \Redis();
//            $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
//            $redis->auth(env('CACHE.PASSWORD'));
//            $redis->select(14);
//            $phonelist = $redis->zRange($smsinfo['sms_phonestr'],$star,$end,'withScore');
//            $redis->expireAt($smsinfo['sms_phonestr'],time()+3600*24*10);
//            $redis->expireAt($smsinfo['sms_phonestr'].'.content',time()+3600*24*10);
//            if(empty($type) || empty($content) || empty($phonelist)){
//                $sms_phonestr = $smsinfo['sms_phonestr'];
//                $message = "未查找到发送短信内容,或者redis key为:$sms_phonestr,[$star,$end]内容为空!";
//                Db::name('messages')->where($where)->update(['sms_status'=>6,'remark'=>$message]);
//                Log::error($message);
//                $data['error_code'] = -1;
//                $data['error_msg'] = '必填项为空';
//                $data['data'] = '';
//                return json($data);
//            }
//
//            if($type == 1){ #亿美
//                if($sms_type == 1){
//                    $result = Sms::YmSendSMS($content, $phonelist);
//                }else{
//                    $contentList = $redis->zRange($smsinfo['sms_phonestr'].'.content',$star,$end,'withScore');
//                    $personalContentList = [];
//                    $number = $end-$star;
//                    for ($i=0;$i<=$number;$i++){
//                        $personalContentList[$phonelist[$i]] = $contentList[$i];
//                    }
//                    $result = Sms::YmPersonalitySMS($personalContentList);
//
//                }
//            }else{
//                #腾域
////                if($smsinfo['sms_category'] == 1){
////                    $result = Sms::TySendSMS($content, $phonelist);
////                }else{
////                    //  $contentList = Cache::store('redis')->handler()->zRange($smsinfo['sms_phonestr'].'.content',$star,$end,'withScore');
////                    $contentList = $redis->zRange($smsinfo['sms_phonestr'].'.content',$star,$end,'withScore');
////                    $personalContentList = [];
////                    $number = $end-$star;
////                    for ($i=0;$i<=$number;$i++){
////                        $personalContentList[$phonelist[$i]] = $contentList[$i];
////                    }
////                    $result = Sms::TyPersonalitySMS($personalContentList);
////                }
//                $sms = Sms::BatchTySendSMS($content,$phonelist,$smsinfo['sms_category']);
//                Log::info(date("Y-m-d H:i:s")." 腾域群发短信结果：".json_encode($sms)."，请求数->发送内容：".$content.'，电话号码：'.json_encode($phonelist).'，短信类型：'.$sms_type);
//                if($sms && isset($sms['code']) && $sms['code'] == 0){
//                    $result = true;
//                }
//            }
//            $data = [];
//            if($result==true){
//                Db::name('messages')->where($where)->update(['sms_status'=>4]);
//                $data['error_code'] = 0;
//                $data['error_msg'] = '';
//                $data['data'] = '';
//            }else{
//                $msg = isset($result['message'])?$result['message']:'短息发送失败';
//                $msg = $msg.json_encode($param);
//                Db::name('messages')->where($where)->update(['sms_status'=>6,"remark"=>$msg]);
//                $data['error_code'] = -1;
//                $data['error_msg'] = '短息发送失败';
//                $data['data'] = [];
//            }
//            return json($data);
        }

    }

    public function send1(){
        $param = $this->request->post();
        Log::info('内网访问成功:'.json_encode($param,JSON_UNESCAPED_UNICODE));
        $type = $param['type'];//1:亿美 2:腾域
        $sms_type = $param['sms_type'];
        $message_id = $param['message_id'];
        $star = $param['star']-1<=0?0:$param['star'];
        $end = $param['end']-1<0?0:$param['end'];
        $where[] = ['id','=',$message_id];
        $where[] = ['sms_status','=',2];
        $smsinfo = Db::name('messages')->where($where)->find();

        if(empty($smsinfo)){
            Log::info('未找到id为$message_id的短信或已经取消发送');

            $data['error_code'] = -1;
            $data['error_msg'] = '未找到短信或已经取消发送';
            $data['data'] = "";
        }
        $content = $smsinfo['sms_content'];

        $redis = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        $redis->auth(env('CACHE.PASSWORD'));
        $redis->select(14);
        //$phonelist = Cache::store('redis')->handler()->zRange($smsinfo['sms_phonestr'],$star,$end,'withScore');
        $phonelist = $redis->zRange($smsinfo['sms_phonestr'],$star,$end,'withScore');
        //$phonelist = array_keys($phonelist);
        $redis->expireAt($smsinfo['sms_phonestr'],time()+3600*24*10);
        $redis->expireAt($smsinfo['sms_phonestr'].'.content',time()+3600*24*10);
        if(empty($type) || empty($content) || empty($phonelist)){
            $sms_phonestr = $smsinfo['sms_phonestr'];
            $message = "未查找到发送短信内容,或者redis key为:$sms_phonestr,[$star,$end]内容为空!";
            Db::name('messages')->where($where)->update(['sms_status'=>6,'remark'=>$message]);
            Log::error($message);
            $data['error_code'] = -1;
            $data['error_msg'] = '必填项为空';
            $data['data'] = '';
            return json($data);
        }
        if($type == 1){ #亿美
            if($sms_type == 1){
                $result = Sms::YmSendSMS($content, $phonelist);
            }else{
                $contentList = $redis->zRange($smsinfo['sms_phonestr'].'.content',$star,$end,'withScore');
                $personalContentList = [];
                $number = $end-$star;
                for ($i=0;$i<=$number;$i++){
                    $personalContentList[$phonelist[$i]] = $contentList[$i];
                }
                $result = Sms::YmPersonalitySMS($personalContentList);

            }
        }else{
            #腾域
            if($sms_type == 1){
                $result = Sms::TySendSMS($content, $phonelist);
            }else{

                //  $contentList = Cache::store('redis')->handler()->zRange($smsinfo['sms_phonestr'].'.content',$star,$end,'withScore');
                $contentList = $redis->zRange($smsinfo['sms_phonestr'].'.content',$star,$end,'withScore');
                $personalContentList = [];
                $number = $end-$star;
                for ($i=0;$i<=$number;$i++){
                    $personalContentList[$phonelist[$i]] = $contentList[$i];
                }
                Log::write('%%%%%%%%%%%');
                Log::write($personalContentList);
                $result = Sms::TyPersonalitySMS($personalContentList);
            }
        }
        $data = [];
        if($result && !$result['code']){
            Db::name('messages')->where($where)->update(['sms_status'=>4]);
            $data['error_code'] = 0;
            $data['error_msg'] = '';
            $data['data'] = '';
        }else{
            $msg = isset($result['message'])?$result['message']:'短息发送失败';
            $msg = $msg.json_encode($param);
            Db::name('messages')->where($where)->update(['sms_status'=>6,"remark"=>$msg]);
            $data['error_code'] = -1;
            $data['error_msg'] = '短息发送失败';
            $data['data'] = $result;
        }
        return json($data);
    }

    /**
     * 审批回调
     * @return \think\response\Json
     */
    public function examincallback(){
        $param = $this->request->post();
        $res = (new ShortMessageService())->callback($param);
       return  json($res);

    }

    /**
     * @Describe:钉钉审批回调
     * <AUTHOR>
     * @Date 2021/9/15 15:23
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function examincallback1(){
        $param = $this->request->post();
        Log::info("钉钉审批回调参数1：".json_encode($param,JSON_UNESCAPED_UNICODE));
//        exit;
        if($param['process_instance']['status'] !== 'COMPLETED'){
            return 0;
        }
        //1成功，2 拒绝
        $examinStatus = $param['process_instance']['result'] == 'agree'?1:2; //1
        $form_component_values = $param['process_instance']['form_component_values'];
        $message_id = ''; //336
        foreach($form_component_values as $value){
            if(!empty($value['name'])){
                if($value['name'] == '短信ID'){
                    $message_id = $value['value'];
                    break;
                }
            }
        }

        //$examinStatus = $param['status'];
        //$message_id = $param['message_id'];
        //$task_url = Route::buildUrl('CallBack/send')->domain(true)->build();
        $task_url = env('ITEM.SMS_URL').'/sms/v3/group/send.html';
        $update['sms_status'] = $examinStatus==1?2:3;
        if($examinStatus == 1){
            //审批通过
            $smsInfo = Db::name('messages')->where([['id', '=', $message_id]])->find();
            $sms_count = $smsInfo['sms_count'];//总条数
            $size = 500;//每次发送短信数量
            $sms_schedule = $smsInfo['sms_schedule'];
            $sms_schedule = strtotime($sms_schedule);
            $nowTime = time()+60*1;//当前时间五分钟后1
            //原计划进行,推送计划任务,分页发送
            if(ceil($sms_count/$size)<=1){
                //任务参数
                $task_data['type'] = $smsInfo['sms_platform'];
                $task_data['message_id'] = $smsInfo['id'];
                $task_data['star'] = 0;
                $task_data['end'] = $size;
                $task_data['sms_type'] = $smsInfo['sms_type'];
                $task_json = json_encode($task_data);
                $sms_schedule = $nowTime < $sms_schedule?$sms_schedule:$nowTime;
                $update['sms_task_id'] = timedTaskadd($sms_schedule, $task_url,$task_json);
                $update['sms_schedule'] = $nowTime < $sms_schedule?$smsInfo['sms_schedule']:date('Y-m-d H:i:s', $sms_schedule);
            }else{
                //分页发送
                $page = ceil($sms_count/$size);
                $update['sms_task_id'] = '';
                $sms_schedule = $nowTime < $sms_schedule?$sms_schedule:$nowTime;
                for($i=1;$i<=$page;$i++){
                    $task_data['type'] = $smsInfo['sms_platform'];
                    $task_data['message_id'] = $smsInfo['id'];
                    $task_data['sms_type'] = $smsInfo['sms_type'];
                    $task_data['star'] = ($i-1)*$size;
                    $task_data['end'] = $i*$size-1;
                    $task_json = json_encode($task_data);
                    $update['sms_task_id'] .= timedTaskadd($sms_schedule+$i-1, $task_url,$task_json).',';
                }
                $update['sms_schedule'] = $sms_schedule == $nowTime?date('Y-m-d H:i:s', $sms_schedule):$smsInfo['sms_schedule'];
            }
        }
        Db::name('messages')->where([['id', '=', $message_id]])->update($update);
        $data['error_code'] = 0;
        $data['error_msg'] = 'OK';
        $data['data'] = [];
        $data['last_update'] = time();
        return json($data);
    }

    /**
     * @方法描述: 短地址短信直接发送
     * @param array $smsinfo 短信信息
     * @param array $param 请求参数
     * @return \think\response\Json
     */
    private function sendShortUrlSms($smsinfo, $param)
    {
        $message_id = intval($param['message_id']);
        $where      = [['id', '=', $message_id]];

        try {
            // 获取手机号列表
            $phoneList = $this->getPhoneList($smsinfo, $param);
            if (empty($phoneList)) {
                Db::name('messages')->where($where)->update(['sms_status' => 6, 'remark' => '未找到发送手机号']);
                return json(['error_code' => -1, 'error_msg' => '未找到发送手机号', 'data' => []]);
            }

            // 调用短地址发送
            $result = Sms::Dh3tShortUrlSend(
                $smsinfo['sms_content'],
                $phoneList,
                $smsinfo['sms_category'],
                $smsinfo['sms_url'],
                intval($smsinfo['sms_url_days']),
                $smsinfo['sms_schedule']
            );

            // 处理发送结果
            if ($result && isset($result['result']) && $result['result'] == 0) {
                // 发送成功
                $update = [
                    'sms_status' => 4, // 已发送
                    'remark'     => '短地址发送成功: ' . (isset($result['desc']) ? $result['desc'] : ''),
                    'sms_msgid'  => isset($result['msgid']) ? $result['msgid'] : ''
                ];
                Db::name('messages')->where($where)->update($update);

                Log::info("短地址短信发送成功 - message_id: {$message_id}, 手机号数量: " . count($phoneList) . ", 结果: " . json_encode($result));

                return json(['error_code' => 0, 'error_msg' => '短地址发送成功', 'data' => $result]);
            } else {
                // 发送失败
                $errorMsg = isset($result['desc']) ? $result['desc'] : '短地址发送失败';
                Db::name('messages')->where($where)->update(['sms_status' => 6, 'remark' => $errorMsg]);

                Log::error("短地址短信发送失败 - message_id: {$message_id}, 错误: " . json_encode($result));

                return json(['error_code' => -1, 'error_msg' => $errorMsg, 'data' => []]);
            }

        } catch (\Exception $e) {
            $errorMsg = '短地址发送异常: ' . $e->getMessage();
            Db::name('messages')->where($where)->update(['sms_status' => 6, 'remark' => $errorMsg]);

            Log::error("短地址短信发送异常 - message_id: {$message_id}, 异常: " . $errorMsg);

            return json(['error_code' => -1, 'error_msg' => $errorMsg, 'data' => []]);
        }
    }

    /**
     * @方法描述: 获取手机号列表
     * @param array $smsinfo 短信信息
     * @param array $param 请求参数
     * @return array
     */
    private function getPhoneList($smsinfo, $param)
    {
        $phoneList = [];

        if ($smsinfo['send_type'] == 1 || $smsinfo['send_type'] == 2) {
            // 根据搜索条件获取手机号
            $searchData = json_decode($smsinfo['search_criteria'], true);
            $searchData['send_type'] = $smsinfo['send_type'];
            $searchData['page'] = 1;
            $searchData['limit'] = 999999; // 获取所有数据

            // 使用 ShortMessageService 获取手机号
            $shortMessageService = new \app\service\ShortMessageService();
            $res = $shortMessageService->getOrderSendSmsPhone($searchData);

            if (isset($res['telephone']) && !empty($res['telephone'])) {
                $phoneList = array_merge($phoneList, $res['telephone']);
            }
        } else {
            // send_type == 3，从Redis获取导入的手机号
            $redis = new \Redis();
            $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
            $redis->auth(env('CACHE.PASSWORD'));
            $redis->select(14);


            //todo ....  redis 连接正常且有值, 为什么查询出为 fales ;
            $phoneList = $redis->zRange($smsinfo['sms_phonestr'], 0, -1);


            $redis->close();
        }

        return array_unique(array_filter($phoneList)); // 去重和过滤空值
    }
}