<?php

// +----------------------------------------------------------------------
// | 缓存设置
// +----------------------------------------------------------------------

return [
    // 默认缓存驱动
    'default' => env('CACHE.DRIVER', 'redis'),

    // 缓存连接方式配置
    'stores'  => [
        'file' => [
            // 驱动方式
            'type'       => 'File',
            // 缓存保存目录
            'path'       => '',
            // 缓存前缀
            'prefix'     => '',
            // 缓存有效期 0表示永久缓存
            'expire'     => 0,
            // 缓存标签前缀
            'tag_prefix' => 'tag:',
            // 序列化机制 例如 ['serialize', 'unserialize']
            'serialize'  => [],
        ],
        //env('redis.type', 'redis'),
        // redis缓存
        'redis'   =>  [
            // 驱动方式
            'type'   => env('CACHE.DRIVER', 'redis'),
            // 服务器地址
            'host'       => env('CACHE.HOST', '*************'),
            //端口号
            'port'  => env('CACHE.PORT', '6379'),
            //密码
            'password'  => env('CACHE.PASSWORD', 'vh@123'),
            //db位置
            'select' => 14,
        ],
    ],
];
