<?php


namespace app\controller;


use app\BaseController;
use think\facade\Filesystem;
use sms\Sms;
use think\facade\Cache;

class Test extends BaseController {
    public function ty(){
        $phoneList = ['18680857113'=>'测试短信2','18523520630'=>'测试短信3','15683953438'=>'测试短信4','18983762612'=>'测试短信0'];
        $result = Sms::TyPersonalitySMS($phoneList);
        dump($result);
    }

    public function addphone(){
        $excel = $this->request->file('excel');
        validate(
            ['file' => ['fileSize' => 1024 * 1024,'fileExt'  => 'xlsx,xls,csv']],
            ['file.fileSize' => '文件太大','file.fileExt' => '不支持的文件后缀',])->check(['file' => $excel]);
        $savename = Filesystem::disk('public')->putFile('excelfile', $excel,'generateName');
        $savename = app()->getRootPath() . 'public/upload/'.$savename;
        $phoneList = readPhoneList($savename);
        foreach($phoneList as $value){
            Cache::push('phonelist', $value);
        }
    }


    public function delphone(){
        $phonelist = Cache::pull('phonelist');
        if(empty($phonelist)){
            $data['code'] = 0;
            $data['msg'] = '没有添加手机号码.';
            return json($data);
        }
        $excel = $this->request->file('excel');
        validate(
            ['file' => ['fileSize' => 1024 * 1024,'fileExt'  => 'xlsx,xls,csv']],
            ['file.fileSize' => '文件太大','file.fileExt' => '不支持的文件后缀',])->check(['file' => $excel]);
        $savename = Filesystem::disk('public')->putFile('excelfile', $excel,'generateName');
        $savename = app()->getRootPath() . 'public/upload/'.$savename;
        $delphoneList = readPhoneList($savename);
        $result = array_filter($phonelist, function($e) use($delphoneList) {
            return  !in_array($e, $delphoneList);
        });
        if(empty($result)){
            $data['code'] = 0;
            $data['msg'] = '剩余手机号:'.count($result);
            return json($data);
        }else{
            foreach($result as $value){
                Cache::push('phonelist', $value);
            }
            $data['code'] = 0;
            $data['msg'] = '剩余手机号:'.count($result);
            return json($data);
        }

    }

    public function dingding(){
        //dingdingUserInfo('15736175219');
        //dingdingUserInfo('18983762612');
        dingdingCreate(15);
    }
}