<?php


namespace app\controller;


use app\BaseController;
use think\Exception;
use think\facade\Db;
use think\facade\Filesystem;
use think\facade\Cache;
use think\facade\Log;

class Phonelist extends BaseController {

    /**
     * 导入需要发送的手机号码
     * 自动删除黑名单用户
     * 自动累加
     * @return \think\response\Json
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     */
    public function import(){
        $excel = $this->request->file('excel');
        $sms_phonestr = $this->request->post('sms_phonestr');
        validate(
            ['file' => ['fileSize' => 1024 * 1024,'fileExt'  => 'xlsx,xls,csv']],
            ['file.fileSize' => '文件太大','file.fileExt' => '不支持的文件后缀',])->check(['file' => $excel]);
        $savename = Filesystem::disk('public')->putFile('excelfile', $excel,'generateName');
        $savename = app()->getRootPath() . 'public/upload/'.$savename;
        $phoneList = readPhoneList($savename);
        Log::info("电话号码",json_encode($phoneList));
        $phonestr = !empty($sms_phonestr)?$sms_phonestr:'phponelist_'.time();
        //除去黑名单电话
        $delphoneList = Db::name('blacklist')->column('phonelist');
        if(!empty($delphoneList)){
            $phoneList = array_filter($phoneList, function($e) use($delphoneList) {
                return  !in_array($e, $delphoneList);
            });
        }
        $redis = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        $redis->auth(env('CACHE.PASSWORD'));
        $redis->select(14);
        foreach($phoneList as $value){
          // Cache::store('redis')->handler()->zadd($phonestr,1,$value);
           $redis->zAdd($phonestr,1,$value);
        }
       // $phoneList = Cache::store('redis')->handler()->zrange($sms_phonestr,0,-1);
        $phoneList = $redis->zRange($sms_phonestr,0,-1);
        foreach($phoneList as $key=>$val){
           // Cache::store('redis')->handler()->zadd($phonestr,$key,$val);
            $redis->zAdd($phonestr,$key,$val);
        }
        $data['error_code'] = 0;
        $data['error_msg'] = '';
        $data['data'] = ['phonestr'=>$phonestr,'total'=>count($phoneList)];
        return json($data);
    }

    /**
     * @return \think\response\Json
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     */
    /*public function batch_del(){
        $sms_phonestr = $this->request->post('sms_phonestr');
        $phonelist = Cache::store('redis')->handler()->zrange($sms_phonestr,0,-1);
        Cache::store('redis')->handler()->zRemRangeByScore($sms_phonestr,0,count($phonelist));
        if(empty($phonelist)){
            $data['error_code'] = 0;
            $data['error_msg'] = '没有添加手机号码.';
            return json($data);
        }
        $excel = $this->request->file('excel');
        validate(
            ['file' => ['fileSize' => 1024 * 1024,'fileExt'  => 'xlsx,xls,csv']],
            ['file.fileSize' => '文件太大','file.fileExt' => '不支持的文件后缀',])->check(['file' => $excel]);
        $savename = Filesystem::disk('public')->putFile('excelfile', $excel,'generateName');
        $savename = app()->getRootPath() . 'public/upload/'.$savename;
        $delphoneList = readPhoneList($savename);
        $phoneList = array_filter($phonelist, function($e) use($delphoneList) {
            return  !in_array($e, $delphoneList);
        });
        if(empty($phoneList)){
            $data['error_code'] = 0;
            $data['error_msg'] = '剩余手机号:'.count($phoneList);
            return json($data);
        }else{
            foreach($phoneList as $value){
                Cache::store('redis')->handler()->zadd($sms_phonestr,1,$value);
            }
            $phoneList = Cache::store('redis')->handler()->zrange($sms_phonestr,0,-1);
            foreach($phoneList as $key=>$val){
                Cache::store('redis')->handler()->zadd($sms_phonestr,$key,$val);
            }
            $data['error_code'] = 0;
            $data['error_msg'] = '剩余手机号:'.count($phoneList);
            return json($data);
        }

    }*/


    /**
     * @Describe:上传excel后回调
     * <AUTHOR>
     * @Date 2021/9/15 16:16
     * @return \think\response\Json
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     */
    public function downloadExcel(){
        $param = $this->request->post();
        $rule = [
            'order'=>'alpha',
            'sort'=>'alpha',
            'page'=>'integer',
            'size'=>'integer',
        ];
        $message = [
            'order'=>'排序字段错误',
            'sort'=>'排序关键字错误',
            'page'=>'页码为整数',
            'size'=>'页码为整数',
        ];
        $checkval = checkParam($param, $rule, $message);
        if( $checkval !== true){
            return json($checkval);
        }
        $sms_phonestr = $param['sms_phonestr'];//redis key值名
        try {
            $addFile = [];
            if(!empty($param['add'])){
                foreach($param['add'] as $value){
                    $value = env('ALIURL').$value;
                    $data = downloadFile($value);
                    if($data['status']){
                        $addFile[] = $data['path'];
                    }else{
                        throw new Exception($data['msg']);
                    }
                }
            }
            $delFile = [];
            if(!empty($param['del'])){
                foreach($param['del'] as $value){
                    $value = env('ALIURL').$value;
                    $data = downloadFile($value);
                    if($data['status']){
                        $delFile[] = $data['path'];
                    }else{
                        throw new Exception($data['msg']);
                    }
                }
            }
            $addFile = array_filter($addFile);
            $delFile = array_filter($delFile);

            //添加的发送电话号码
            $redis = new \Redis();
            $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
            $redis->auth(env('CACHE.PASSWORD'));
            $redis->select(14);
            $addphoneList = $redis->lRange($sms_phonestr,0,-1);
            if(is_array($addFile) && !empty($addFile)){
                foreach($addFile as $value){
                    $phoneList = readPhoneList($value);
                    $addphoneList = array_merge($addphoneList,$phoneList);
                }
            }
            $addphoneList = array_flip($addphoneList);
            $addphoneList = array_flip($addphoneList);//反转去重

            //除去黑名单电话
            $delphoneList = Db::name('blacklist')->column('phonelist');
            if(is_array($delFile) && !empty($delFile)){
                foreach($delFile as $value){
                    $delphoneList = array_merge($delphoneList,readPhoneList($value));
                }
            }
            $delphoneList = array_flip($delphoneList);
            $delphoneList = array_flip($delphoneList);

            $addphoneList = array_filter($addphoneList, function($e) use($delphoneList) {
                return  !in_array($e, $delphoneList);
            });
            $addphoneList = array_merge($addphoneList);

            print_r($delphoneList);die;

            $elems[] = $sms_phonestr;
            foreach($addphoneList as $key=>$value){
                $elems[] = $key;
                $elems[] = $value;
            }
            $redis->del($sms_phonestr);
            $limit = 9999;
            $total = count($addphoneList);
            $size =  ceil(intval(count($addphoneList))/$limit);
            for($i = 1; $i <= $size; $i++) {
                $offset=($i-1)*$limit;
                $userIdArr = array_slice($addphoneList, intval($offset), intval($limit));
                $redis->rPush($sms_phonestr,implode(",",$userIdArr));
            }
          //  call_user_func_array([Cache::store('redis')->handler(), 'zadd'], $elems);
            $returndata['error_code'] = 0;
            $returndata['error_msg'] = '添加成功';
          //  $returndata['data'] = ['total'=>Cache::store('redis')->handler()->zCard($sms_phonestr)];
            $returndata['data'] = ['total'=>$total];
            return json($returndata);
        }catch (Exception $exception){
            $returndata['error_code'] = -1;
            $returndata['error_msg'] = $exception->getMessage();
            $returndata['data'] = [];
            return json($returndata);
        }
    }
}