<?php
declare (strict_types = 1);

namespace app;
use GuzzleHttp\Exception\RequestException;
use think\facade\Log;

/**
 * 自定义请求类
 */
class CommonHttpRequest
{
    public function httpRequest(string $url, array $params = [], string $method = 'POST', array $configs = [], string $contentType = 'form_params')
    {
        $timeout = env('TIMEOUT',5);
        $configs['timeout'] = $configs['timeout'] ?? $timeout;
        $client = new \GuzzleHttp\Client($configs);
        $params = strtoupper($method) == 'GET' ? ['query' => $params] : [$contentType => $params];

        try {
            $request = $client->request($method, $url, $params);
            $return = $request->getBody()->getContents();
        } catch (RequestException $e) {
            $message = $e->getMessage();
            $return = [
                'status' =>'fail',
                'errorCode'=>'-1',
                'msg'=>$message,
                'data'=> []
            ];
            //请求失败记录日志信息
            $this->recordErrorLog($message);
        }

        if(!is_array($return)){
            $response = json_decode($return, true);
        }else{
            $response = $return;
        }

        return $response;
    }

    public function httpGet(string $url, array $params = [], array $configs = [])
    {

        $timeout = env('TIMEOUT',5);
        $configs['timeout'] = $configs['timeout'] ?? $timeout;
        $client = new \GuzzleHttp\Client($configs);
        $params = ['query' => $params];

        try {
            $request = $client->request('GET', $url, $params);
            $return = $request->getBody()->getContents();
        } catch (RequestException $e) {
            $message = $e->getMessage();
            $return = [
                'status' =>'fail',
                'errorCode'=>'-1',
                'msg'=>$message,
                'data'=> []
            ];
            //请求失败记录日志信息
            $this->recordErrorLog($message);
        }

        if(!is_array($return)){
            $response = json_decode($return, true);
        }else{
            $response = $return;
        }

        return $response;
    }

    public function httpPost(string $url, array $params = [], array $configs = [], string $contentType = 'form_params')
    {
        $timeout = env('TIMEOUT',5);
        $configs['timeout'] = $configs['timeout'] ?? $timeout;
        $client = new \GuzzleHttp\Client($configs);
        $params = [$contentType => $params];

        try {
            $request = $client->request('POST', $url, $params);
            $return = $request->getBody()->getContents();
        } catch (RequestException $e) {
            $message = $e->getMessage();
            $return = [
                'status' =>'fail',
                'errorCode'=>'-1',
                'msg'=>$message,
                'data'=> []
            ];
            //请求失败记录日志信息
            $this->recordErrorLog($message);
        }

        if(!is_array($return)){
            $response = json_decode($return, true);
        }else{
            $response = $return;
        }

        return $response;
    }

    /**
     * http post请求，参数为json字符串
     * @param $url
     * @param $data_string
     * @return bool|string
     */
    function httpPostString($url, $data_string) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'X-AjaxPro-Method:ShowList',
                'Content-Type: application/json; charset=utf-8',
                'Content-Length: ' . strlen($data_string))
        );
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        $data = curl_exec($ch);
        curl_close($ch);
        return $data;
    }

    /**
     * 请求失败记录日志
     * @param $e 异常信息
     */
    private function recordErrorLog($errorInfo)
    {
        Log::record($errorInfo,'error');
    }
}