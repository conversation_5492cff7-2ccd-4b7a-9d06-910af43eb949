<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

Route::get('/', function(){
    return 'ok-tp6-sms';
});

Route::group('admin',function (){
    Route::get('demo','index/adminDemo');
});

Route::rule('log', function () {
    $error = app()->getRuntimePath() . "log/" . date("Ym") . "/" . date("d") . ".log";
    dump(file_get_contents($error));exit;
});

Route::rule('cl', function () {
    $error = app()->getRuntimePath() . "log/" . date("Ym") . "/" . date("d") . ".log";
    file_put_contents($error, "");
});

Route::group('sms/v3/group',function (){
    Route::post('send','CallBack/send');
    Route::post('examincallback','CallBack/examincallback');
    Route::post('search', 'ShortMessage/search');
    Route::post('create','ShortMessage/create' );
    Route::post('sendUserSms','ShortMessage/sendUserSms' );
    Route::post('import', 'Phonelist/import');
    Route::post('batch_del', 'Phonelist/batch_del');
    Route::post('downloadExcel', 'Phonelist/downloadExcel');
    Route::post('cancel', 'ShortMessage/cancel');
    Route::get('dingding', 'Test/dingding');
    Route::post('sendSms','ShortMessage/sendSms' ); #短信发送 腾域
    Route::post('sendSmsOne','ShortMessage/oneTySendSMS' ); #短信发送 一对一
    Route::post('voiceSend','ShortMessage/voiceSend' ); #语音通知 腾域
    Route::post('sendSmsNote','ShortMessage/sendSmsNote' ); #短信发送 腾域 笔记项目使用
});

Route::group('other',function (){
    Route::get('demo','index/otherDemo');
});

