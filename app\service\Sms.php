<?php


namespace app\service;


use think\facade\Db;
use think\facade\Log;

class Sms{

    public static $TyErrorArr = [
            1 => '帐号名或密码为空',
            2 => '帐号名或密码错误',
            3 => '帐号已被锁定',
            4 => '此帐号业务未开通',
            5 => '帐号余额不足',
            6 => '缺少发送号码',
            7 => '发送号码数超过10000',
            8 => '发送消息内容为空',
            9 => '无效的RCS模板ID',
            10 => '非法的IP地址，提交来源IP地址与帐号绑定IP不一致',
            11 => '24小时发送时间段限制',
            12 => '定时发送时间错误或超过15天',
            13 => '请求过于频繁，每次请求数据时间间隔为一分钟',
            14 => '错误的用户扩展码',
            50 => '缺少模板标题',
            51 => '缺少模板内容',
            52 => '模板内容不全',
            53 => '不支持的模板帧类型',
            54 => '不支持的文件类型',
            99 => '错误的请求JSON字符串',
            500 => '系统异常',
        ];

    const Dh3tErrorArr = [
        0 => '提交成功',
        1 => '账号或密码错误',
        3 => 'msgid太长，不得超过64位',
        4 => '错误号码/限制运营商号码',
        5 => '手机号码个数超过最大限制',
        6 => '短信内容超过最大限制',
        7 => '扩展子号码无效',
        8 => '定时时间格式错误',
        14 => '手机号码为空',
        19 => '用户被禁发或禁用',
        20 => 'ip鉴权失败',
        21 => '短信内容为空',
        24 => '无可用号码',
        25 => '批量提交短信数超过最大限制',
        98 => '系统正忙',
        99 => '消息格式错误',
    ];


    /**
     * 腾域短信发送
     * 上限1000
     * @param $content
     * @param array $phoneList
     * @return bool|string
     */
    public static function TySendSMS($content,array $phoneList){
        //短信API接口地址
        $url = 'http://*************:8001/sms/api/sendMessage';
        $post_data['userName'] = 'jyw';//系统用户名
        $post_data['password'] = '888666';//系统密码
        $post_data['content'] = '【酒云网】'.$content;//短信内容，记得带上签名
        $post_data['phoneList'] = $phoneList;//手机号码list
        $post_data['callData'] = '';//回传参数，可为空
        $json = json_encode($post_data);
        $res = self::http_request($url,$json);
        //{"code":0,"message":"处理成功","msgId":3418351095}
        $res = json_decode($res,true);
        Log::write($res);
        if($res['code'] == 0){
            return true;
        }else{
            Log::error('腾域短信发送失败,错误原因:'.self::$TyErrorArr[$res['code']]);
            return $res;
        }
    }

    /**
     * 腾域个性化
     * 上限1000
     * @param $arr[18645136154=>'短信内容']
     * @return bool|string
     */
    public static function TyPersonalitySMS($arr){
        //短信API接口地址
        $url = 'http://*************:8001/sms/api/batchSendMessage';
        $messageList = [];
        array_walk($arr, function($val,$key)use (&$messageList){
            $message = [
                'phone'=> $key,
                'content'=> "【酒云网】".$val,
            ];
            array_push($messageList,$message);
        });
        $post_data['userName'] = '酒云网';//系统用户名 //接手之前写的 jyw
        $post_data['password'] = '239233';//系统密码 //接手之前写的 888666
        $post_data['messageList'] = $messageList;
        $post_data['callData'] = '';//回传参数，可为空
        $json = json_encode($post_data,JSON_UNESCAPED_UNICODE);
        $res = self::http_request($url,$json);
        //{"code":0,"message":"处理成功","msgId":3418351095}
        $res = json_decode($res,true);
        Log::write('!!!!!!!!!!!');
        Log::write($res);
        if($res['code'] == 0){
            return true;
        }else{
            Log::error('腾域短信发送失败,错误原因:'.self::$TyErrorArr[$res['code']]);
            return $res;
        }
    }

    /**
     * 亿美短息发送
     * 上限500
     * @param $content
     * @param array $phoneList
     * @return bool|mixed
     */
    public static function YmSendSMS($content,array $phoneList){
        $AppID = "EUCP-EMY-SMS1-3J1WU";
        $AesPwd = "C31F96C381CA5774";
        $phoneList = arrayToStr($phoneList);
        // 另外，如果包含特殊字符，需要对内容进行urlencode
        $content = "【酒云网】".$content."TD退订";
        $timestamp = date("YmdHis");
        $sign = md5($AppID.$AesPwd.$timestamp);
        // 如果您的系统环境不是UTF-8，需要转码到UTF-8。如下：从gb2312转到了UTF-8
        $content = mb_convert_encoding( $content,"UTF-8","auto");
        $data = array(
            "appId" => $AppID,
            "timestamp" => $timestamp,
            "sign" => $sign,
            "mobiles" => $phoneList,
            "content" =>  $content,
            "customSmsId" => "",
            "extendedCode" => ""
        );
        $url = 'http://www.btom.cn:8080/simpleinter/sendSMS';
        $data = http_build_query($data);
        $result = self::http_request($url, $data,'ym');
        $res = json_decode($result,true);
        if($res['code'] == 'SUCCESS'){
            return true;
        }else{
            Log::error('亿美短信发送失败,错误原因:'.$result);
            return $res;
        }
    }

    /**
     * 亿美个性短信发送
     * 上限500
     * @param $arr [18645136154=>'短信内容']
     */
    public static function YmPersonalitySMS($arr){
        $AppID = "EUCP-EMY-SMS1-3J1WU";
        $AesPwd = "C31F96C381CA5774";
        $arr = array_map(function($val){
            // 如果您的系统环境不是UTF-8，需要转码到UTF-8。如下：从gb2312转到了UTF-8
            $val = mb_convert_encoding("【酒云网】".$val."TD退订","UTF-8","auto");
            return $val;
        }, $arr);
        $timestamp = date("YmdHis");
        $sign = md5($AppID.$AesPwd.$timestamp);


        $data = array(
            "appId" => $AppID,
            "timestamp" => $timestamp,
            "sign" => $sign,
            "customSmsId" => "10001",
        );
        $data = $data+$arr;
        $url = 'http://www.btom.cn:8080/simpleinter/sendPersonalitySMS';
        $data = http_build_query($data);
        $result = self::http_request($url, $data,'ym');
        $res = json_decode($result,true);
        if($res['code'] == 'SUCCESS'){
            return true;
        }else{
            Log::error('亿美短信发送失败,错误原因:'.$result);
            return $res;
        }
    }

    public static function http_request($url,$data,$type='ty'){
        if (empty($url) || empty($data)) {
            return false;
        }
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_POST, TRUE);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        if($type == 'ty'){
            //腾域需要把数组转成json字符串curlopt_httpheader
            $header[] = 'Content-Type: application/json; charset=utf-8';
            $header[] = 'Content-Length: ' . strlen($data);
            curl_setopt($curl, CURLOPT_HTTPHEADER,$header);
        }
        $output = curl_exec($curl);
        curl_close($curl);
        return $output;
    }

    /**
     * @param $content
     * @param $telephone
     * @return bool|int|string
     */
    public static function createSendSms($content, $telephone)
    {

        //短信API接口地址
        $url = 'http://*************:8001/sms/api/sendMessage';
        $post_data['userName'] = 'jyw';//系统用户名
        $post_data['password'] = '888666';//系统密码
        $post_data['content'] = '【酒云网】'.$content;//短信内容，记得带上签名
        $post_data['phoneList'] = explode(',',$telephone);//手机号码list
        $post_data['callData'] = '';//回传参数，可为空
        $json = json_encode($post_data);
        $res = self::http_request($url,$json);
        //{"code":0,"message":"处理成功","msgId":3418351095}
        $res = json_decode($res,true);
        $data=[
            'telephone'=>$telephone,
            'content'=>$content,
            'created_time' => date('Y-m-d H:i:s'),
            'code'=>strval($res['code'])
        ];
        Db::name('sms_log')->insert($data);
        if($res['code'] == 0){
            return true;
        }else{
            Log::error('腾域短信发送失败,错误原因:'.self::$TyErrorArr[$res['code']]);
            return self::$TyErrorArr[$res['code']];
        }

    }

    /**
     *语音电话通知
     * @param $params
     * @return mixed
     */
    public static function voiceSend($params)
    {
        $data = [
            'method'=>"Submit",
            "account"=>'VM70519237',
            "password"=>'de4e3c3b2932e4ea8a14ded24a909240',
            "mobile"=>$params['telephone'],
            ///"content"=>'您的订单号是：75231。已由顺风快递发出，请注意查收',
            "content"=>'您在酒云网尾号为'.$params['sub_order_no'].'包裹已超过24小时未领取，请尽快前往站点领取您的包裹。',
            "time"=>'',
            "format"=>'json',
        ];

        $url = "http://api.vm.it1688.com.cn/webservice/voice.php";
        $result=curlRequest($url,$data,[],"GET");
        $res = json_decode($result,true);

        $logData=[
            'telephone'=>$params['telephone'],
            'content'=>$data['content'],
            'created_time' => date('Y-m-d H:i:s'),
            'code'=>strval($res['code'])
        ];
        Db::name('sms_log')->insert($logData);

        return $res;

    }

    /**
     * 腾域批量发送短信
     * 上限10000
     * $url = https://send.it1688.com.cn:8443/sms/api/sendMessage
     * @param $content
     * @param array $phoneList
     * @param $type
     * @param string $url 长地址URL，如果传入则使用短地址发送
     * @param int $days 短地址有效天数，默认5天
     * @param string $sendtime 定时发送时间，格式：yyyy-MM-dd HH:mm:ss，空则立即发送
     * @return bool|mixed
     */
    public static function BatchTySendSMS($content,array $phoneList,$type, $url = '', $days = 5, $sendtime = ''){
        // 如果传入了URL参数且内容包含该URL，直接使用短地址发送（已内置分批逻辑）
        if (!empty($url) && strpos($content, $url) !== false) {
            return self::Dh3tShortUrlSend($content, $phoneList, $type, $url, $days, $sendtime);
        }

        // 普通短信发送，按1000条分批
        $tmpArr = [];
        $count = count($phoneList);
        $res = '未推送';
        foreach ($phoneList as $k => $v){
            $tmpArr[] = $v;
            if (($k + 1) % 1000 == 0 || ($k + 1) == $count){
                $res =  self::Dh3tSubmit($content, $tmpArr, $type, '', 0); // 传空URL避免重复判断
                $tmpArr = [];
            }
        }
        return $res;
        //短信API接口地址
        $url = 'https://send.it1688.com.cn:8443/sms/api/sendMessage';
        $source = '【酒云网】';
        if (in_array($type, [3, 4])) {
            $source = '【酒爱拍】';
        }elseif (in_array($type, [5, 6])){
            $source = '【酒云公社】';
        }elseif (in_array($type, [7, 8])){
            $source = '【木兰朵酒庄】';
        }elseif (in_array($type, [9, 10])){
            $source = '【兔子星球】';
        }
        if ($type == 1 || $type == 3 || $type == 5 || $type == 7){ #通知
            $userName = env("SMS.TY_NOTICE_USER_NAME"); #用户名
            $password = env("SMS.TY_NOTICE_PASSWORD");  #密码
        }else{ # 营销短信
            $userName = env("SMS.TY_MARKETING_USER_NAME");
            $password = env("SMS.TY_MARKETING_PASSWORD");
        }
        $post_data['userName'] = $userName;//系统用户名
        $post_data['content'] = $source.$content;//短信内容，记得带上签名
        $post_data['phoneList'] = $phoneList;//手机号码list
        $timestamp = getMillisecond();//
        $post_data['timestamp'] = intval($timestamp);//
        $sig = md5($userName.$post_data['content'].$post_data['timestamp'].md5($password));
        $post_data['sign'] = $sig;//MD5(userName + content + timestamp + MD5(password))
        $json = json_encode($post_data);
        $res = curlRequest($url,$json);
        $res = json_decode($res,true);
        //$res = json_decode($res,true);
        Log::write($res);
        $data=[
            'telephone'=>implode(',',$phoneList),
            'content'=>$content,
            'created_time' => date('Y-m-d H:i:s'),
            'code'=>strval($res['code']),
            'type'         => $type,
            'platform'     => '腾域'
        ];
        Db::name('sms_log')->insert($data);
        if($res['code'] == 0){
            return $res;
        }else{
            Log::error('腾域短信发送失败,错误原因:'.self::$TyErrorArr[$res['code']]);
            return self::$TyErrorArr[$res['code']];
        }
    }
    /**
     * 腾域一对一发送短信
     * 上限1000
     * $url = https://send.it1688.com.cn:8443/sms/api/sendMessage
     * @param $content
     * @param array $phone
     * @param $type
     * @param string $url 长地址URL，如果传入则使用短地址发送
     * @param int $days 短地址有效天数，默认5天
     * @param string $sendtime 定时发送时间，格式：yyyy-MM-dd HH:mm:ss，空则立即发送
     * @return bool|mixed
     */
    public static function OneTySendSMS($content,array $phone,$type, $url = '', $days = 5, $sendtime = ''){
        // 如果传入了URL参数且内容包含该URL，直接使用短地址发送
        if (!empty($url) && strpos($content, $url) !== false) {
            return self::singleShortUrlSend($content, $phone, $type, $url, $days, $sendtime);
        }

        // 普通短信发送
        return self::Dh3tSubmit($content, $phone, $type, '', 0); // 传空URL避免重复判断
        //短信API接口地址
        $url = 'https://send.it1688.com.cn:8443/sms/api/sendMessageOne';
        $source = '【酒云网】';
        if (in_array($type, [3, 4])) {
            $source = '【酒爱拍】';
        }elseif (in_array($type, [5, 6])){
            $source = '【酒云公社】';
        }elseif (in_array($type, [7, 8])){
            $source = '【木兰朵酒庄】';
        }elseif (in_array($type, [9, 10])){
            $source = '【兔子星球】';
        }
        if ($type == 1 || $type == 3 || $type == 5 || $type == 7){ #通知
            $userName = env("SMS.TY_NOTICE_USER_NAME");#用户名
            $password = env("SMS.TY_NOTICE_PASSWORD");#密码
        }else{ # 营销短信
            $userName = env("SMS.TY_MARKETING_USER_NAME");
            $password = env("SMS.TY_MARKETING_PASSWORD");
        }

        $post_data['userName'] = $userName;//系统用户名
        $messageList=[];
        foreach ($phone as $p){
            $messageList[]=[
                'phone'=>$p,
                'content'=> $source.$content,#短信内容，记得带上签名
            ];
        }
        $post_data['messageList'] = $messageList;#发送对象及其电话号码
        $timestamp = getMillisecond();#当前时间戳 精确到毫秒
        $post_data['timestamp'] = intval($timestamp);#当前时间戳 精确到毫秒
        $sig = md5($userName.$post_data['timestamp'].md5($password));
        $post_data['sign'] = $sig;//MD5(userName + content + timestamp + MD5(password))
        $json = json_encode($post_data);
        $res = curlRequest($url,$json);
        $res = json_decode($res,true);
        //$res = json_decode($res,true);
        Log::write($res);
        $data=[
            'telephone'=>implode(',',$phone),
            'content'=>$content,
            'created_time' => date('Y-m-d H:i:s'),
            'code'=>strval($res['code']),
            'type'         => $type,
            'platform'     => '腾域'
        ];
        Db::name('sms_log')->insert($data);
        if($res['code'] == 0){
            return $res;
        }else{
            Log::error('腾域短信发送失败,错误原因:'.self::$TyErrorArr[$res['code']]);
            return self::$TyErrorArr[$res['code']];
        }
    }

    /**
     * 方法描述：腾域发送短信 笔记使用
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2024/3/18 17:42
     * @param $content
     * @param array $phone
     * @param $type
     * @return mixed|string
     */
    public static function OneTySendSMSNote($content,array $phone,$type){
        return self::Dh3tSubmitWithSign($content,$phone, $type);
        //短信API接口地址
        $url = 'https://send.it1688.com.cn:8443/sms/api/sendMessageOne';
        if ($type == 1){ #通知
            $userName = env("SMS.TY_NOTICE_USER_NAME");#用户名
            $password = env("SMS.TY_NOTICE_PASSWORD");#密码
        }else{ # 营销短信
            $userName = env("SMS.TY_MARKETING_USER_NAME");
            $password = env("SMS.TY_MARKETING_PASSWORD");
        }

        $post_data['userName'] = $userName;//系统用户名
        $messageList=[];
        foreach ($phone as $p){
            $messageList[]=[
                'phone'=>$p,
                'content'=> $content,#短信内容，记得带上签名
            ];
        }
        $post_data['messageList'] = $messageList;#发送对象及其电话号码
        $timestamp = getMillisecond();#当前时间戳 精确到毫秒
        $post_data['timestamp'] = intval($timestamp);#当前时间戳 精确到毫秒
        $sig = md5($userName.$post_data['timestamp'].md5($password));
        $post_data['sign'] = $sig;//MD5(userName + content + timestamp + MD5(password))
        $json = json_encode($post_data);
        $res = curlRequest($url,$json);
        $res = json_decode($res,true);
        //$res = json_decode($res,true);
        Log::write($res);
        $data=[
            'telephone'=>implode(',',$phone),
            'content'=>$content,
            'created_time' => date('Y-m-d H:i:s'),
            'code'=>strval($res['code']),
            'type'         => $type,
            'platform'     => '腾域'
        ];
        Db::name('sms_log')->insert($data);
        if($res['code'] == 0){
            return $res;
        }else{
            Log::error('腾域短信发送失败,错误原因:'.self::$TyErrorArr[$res['code']]);
            return self::$TyErrorArr[$res['code']];
        }
    }


    /**
     * @方法描述: 大汉通信云发送短信（普通短信，不处理短地址）
     * @Date 2024/4/7 15:59
     * @param $content
     * @param array $phone
     * @param $type
     * @param string $url 预留参数，当前版本不使用
     * @param int $days 预留参数，当前版本不使用
     * @return bool|mixed
     */
    public static function Dh3tSubmit($content,array $phone, $type, $url = '', $days = 5)
    {
        if (preg_match("/【酒爱拍】|【酒云网】|【WineNotes】|【酒云公社】|【木兰朵酒庄】|【兔子星球】/", $content)){
            return self::Dh3tSubmitWithSign($content, $phone, $type);
        }
        $url = 'http://www.dh3t.com/json/sms/Submit';
        if ($type == 1 || $type == 3 || $type == 5 || $type == 7 || $type == 9){
            $content = str_replace("拒收请回复R。", '', $content);
            $content = str_replace("退订回T", '', $content);
            $account = env('SMS.DH3T_ACCOUNT');
            $password = env('SMS.DH3T_MD5_PASSWORD');
        }else{
            $account = env('SMS.DH3T_MARKET_ACCOUNT');
            $password = env('SMS.DH3T_MARKET_MD5_PASSWORD');
        }
        if (mb_strlen($content, 'utf-8') > 1000) return '短信内容过长, 最多1000个汉字';
        if (count($phone) > 1000) return '手机号码过多, 最多1000个';
        $phoneStr = is_array($phone) ? implode(',', $phone) : $phone;

        $source = '【酒云网】';
        if (in_array($type, [3, 4])) {
            $source = '【酒爱拍】';
        }elseif (in_array($type, [5, 6])){
            $source = '【酒云公社】';
        }elseif (in_array($type, [7, 8])){
            $source = '【木兰朵酒庄】';
        }elseif (in_array($type, [9, 10])){
            $source = '【兔子星球】';
        }
        $data = [];
        $data['account'] = $account;
        $data['password'] = $password;
        $data['content'] = $content;
        $data['sign'] = $source;
        $data['phones'] = $phoneStr;
        $res = curlRequest($url, json_encode($data));
        $res = json_decode($res,true);

        Db::name('sms_log')->insert([
            'telephone'    => $phoneStr,
            'content'      => $content,
            'created_time' => date('Y-m-d H:i:s'),
            'code'         => strval($res['result']),
            'type'         => $type,
            'platform'     => '大汉云通信'
        ]);
        $res['code'] = $res['result'];
        if($res['code'] == 0){
            return $res;
        }else{
            $msg = self::Dh3tErrorArr[$res['code']] ?? '未知错误';
            Log::error('大汉通信云短信发送失败,错误原因:'.$msg);
            return $msg;
        }
    }


    /**
     * @方法描述: 大汉通信云发送短信 签名在内容
     * @Date 2024/4/7 15:59
     * @param array $param 请求参数
     */
    public static function Dh3tSubmitWithSign($content, $phoneList, $type)
    {
        $url = 'http://www.dh3t.com/json/sms/SubmitWithSign';
        if ($type == 1 || $type == 3 || $type == 5){
            $content = str_replace("拒收请回复R。", '', $content);
            $content = str_replace("退订回T", '', $content);
            $account = env('SMS.DH3T_ACCOUNT');
            $password = env('SMS.DH3T_MD5_PASSWORD');
        }else{
            $account = env('SMS.DH3T_MARKET_ACCOUNT');
            $password = env('SMS.DH3T_MARKET_MD5_PASSWORD');
        }

        if (mb_strlen($content, 'utf-8') > 1000) return '短信内容过长, 最多1000个汉字';
        if (count($phoneList) > 1000) return '手机号码过多, 最多1000个';
        $phoneStr = is_array($phoneList) ? implode(',', $phoneList) : $phoneList;
        $data = [
            'account' => $account,
            'password' => $password,
            'content' => $content,
            'phones' => $phoneStr,
        ];
        $res = curlRequest($url, json_encode($data));
        $res = json_decode($res,true);

        Db::name('sms_log')->insert([
            'telephone'    => $phoneStr,
            'content'      => $content,
            'created_time' => date('Y-m-d H:i:s'),
            'code'         => strval($res['result']),
            'type'         => $type,
            'platform'     => '大汉云通信'
        ]);
        $res['code'] = $res['result'];
        if($res['code'] == 0){
            return $res;
        }else{
            $msg = self::Dh3tErrorArr[$res['code']] ?? '未知错误';
            Log::error('大汉通信云短信发送失败,错误原因:'.$msg);
            return $msg;
        }
    }

    /**
     *语音电话通知 @todo
     * @param $params
     * @return mixed
     */
    public static function Dh3tSubmitVoc($params)
    {
        // $url = 'http://voice.3tong.net/json/voiceSms/SubmitVoc';
    }

    /**
     * @方法描述: 大汉通信云短地址发送短信（一次性全部发送，不分批）
     * @Date 2024/12/23
     * @param $content 短信内容
     * @param array $phoneList 手机号数组
     * @param $type 短信类型
     * @param $url 长地址URL
     * @param int $days 短地址有效天数，默认5天
     * @param string $sendtime 定时发送时间，格式：yyyy-MM-dd HH:mm:ss，空则立即发送
     * @return array|bool
     */
    public static function Dh3tShortUrlSend($content, array $phoneList, $type, $url, $days = 5, $sendtime = '')
    {
        if (mb_strlen($content, 'utf-8') > 1000) return ['result' => -1, 'desc' => '短信内容过长, 最多1000个汉字'];
        if (empty($url)) return ['result' => -1, 'desc' => 'URL地址不能为空'];
        if ($days < 1 || $days > 180) $days = 5; // 默认5天，最大180天

        // 短地址发送不分批，一次性全部发送，方便统计点击数据
        return self::singleShortUrlSend($content, $phoneList, $type, $url, $days, $sendtime);
    }

    // 注意：短地址发送不再分批，已移除 batchShortUrlSend 方法
    // 短地址需要在同一批次发送，方便统计点击数据

    /**
     * @方法描述: 单批短地址发送
     * @param $content 短信内容
     * @param array $phoneList 手机号数组
     * @param $type 短信类型
     * @param $url 长地址URL
     * @param int $days 短地址有效天数
     * @param string $sendtime 定时发送时间，格式：yyyy-MM-dd HH:mm:ss，空则立即发送
     * @return array
     */
    private static function singleShortUrlSend($content, array $phoneList, $type, $url, $days, $sendtime = '')
    {
        $url_api = 'http://www.dh3t.com/json/sms/ShortUrlSend';

        // 根据类型设置签名
        $sign = '【酒云网】';
        if (in_array($type, [3, 4])) {
            $sign = '【酒爱拍】';
        }elseif (in_array($type, [5, 6])){
            $sign = '【酒云公社】';
        }elseif (in_array($type, [7, 8])){
            $sign = '【木兰朵酒庄】';
        }elseif (in_array($type, [9, 10])){
            $sign = '【兔子星球】';
        }

        // 处理短信内容，如果内容中没有签名，则添加签名
        if (!preg_match("/【酒爱拍】|【酒云网】|【WineNotes】|【酒云公社】|【木兰朵酒庄】|【兔子星球】/", $content)) {
            $content = $sign . $content;
        }

        if ($type == 1 || $type == 3 || $type == 5 || $type == 7 || $type == 9){
            $content = str_replace("拒收请回复R。", '', $content);
            $content = str_replace("退订回T", '', $content);
            $account = env('SMS.DH3T_ACCOUNT');
            $password = env('SMS.DH3T_MD5_PASSWORD');
        }else{
            $account = env('SMS.DH3T_MARKET_ACCOUNT');
            $password = env('SMS.DH3T_MARKET_MD5_PASSWORD');
        }

        // 动态生成包含手机号的zip文件
        $zipFilePath = self::createPhoneZipFile($phoneList);
        if (!$zipFilePath) {
            return ['result' => -1, 'desc' => '生成手机号文件失败'];
        }

        try {
            // 准备POST数据
            // 根据大汉三通短地址接口文档：签名可以放在sign参数中，也可以放在content开头（sign为空）
            // 这里采用签名放在content开头的方式，sign参数为空
            $postData = [
                'account' => $account,
                'password' => $password,
                'url' => $url,
                'days' => $days,
                'sign' => '', // 签名为空，因为签名已经包含在content中
                'content' => $content, // content中已包含签名
                'sendtime' => $sendtime, // 定时发送时间，空则立即发送
            ];

            // 使用cURL发送带文件的POST请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url_api);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, [
                'account' => $postData['account'],
                'password' => $postData['password'],
                'url' => $postData['url'],
                'days' => $postData['days'],
                'sign' => $postData['sign'],
                'content' => $postData['content'],
                'sendtime' => $postData['sendtime'],
                'phonesFile' => new \CURLFile($zipFilePath, 'application/zip', 'phones.zip')
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            // 删除临时zip文件
            if (file_exists($zipFilePath)) {
                unlink($zipFilePath);
            }

            if ($response === false || $httpCode !== 200) {
                return ['result' => -1, 'desc' => '网络请求失败'];
            }

            $res = json_decode($response, true);
            if (!$res) {
                return ['result' => -1, 'desc' => '响应数据格式错误'];
            }

            // 记录短地址发送日志
            Db::name('sms_log')->insert([
                'telephone'    => implode(',', $phoneList),
                'content'      => $content,
                'created_time' => date('Y-m-d H:i:s'),
                'code'         => strval($res['result']),
                'type'         => $type,
                'platform'     => '大汉云通信-短地址',
                'url'          => $url,
                'days'         => $days,
                'msgid'        => isset($res['msgid']) ? $res['msgid'] : ''
            ]);

            return $res;

        } catch (\Exception $e) {
            // 删除临时zip文件
            if (file_exists($zipFilePath)) {
                unlink($zipFilePath);
            }
            return ['result' => -1, 'desc' => '发送异常: ' . $e->getMessage()];
        }
    }

    /**
     * @方法描述: 创建包含手机号的zip文件
     * @param array $phoneList 手机号数组
     * @return string|false 返回zip文件路径，失败返回false
     */
    private static function createPhoneZipFile(array $phoneList)
    {
        $tempDir = app()->getRuntimePath() . "temp";
        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0777, true);
        }
        $txtFileName = 'phones_' . time() . '.txt';
        $zipFileName = 'phones_' . time() . '.zip';
        $txtFilePath = $tempDir . DIRECTORY_SEPARATOR . $txtFileName;
        $zipFilePath = $tempDir . DIRECTORY_SEPARATOR . $zipFileName;

        try {
            // 创建txt文件，每行一个手机号
            $txtContent = implode("\n", $phoneList);
            if (file_put_contents($txtFilePath, $txtContent) === false) {
                return false;
            }

            // 创建zip文件
            $zip = new \ZipArchive();
            if ($zip->open($zipFilePath, \ZipArchive::CREATE) !== TRUE) {
                if (file_exists($txtFilePath)) unlink($txtFilePath);
                return false;
            }

            $zip->addFile($txtFilePath, $txtFileName);
            $zip->close();

            // 删除临时txt文件
            if (file_exists($txtFilePath)) {
                unlink($txtFilePath);
            }

            return $zipFilePath;

        } catch (\Exception $e) {
            // 清理临时文件
            if (file_exists($txtFilePath)) unlink($txtFilePath);
            if (file_exists($zipFilePath)) unlink($zipFilePath);
            return false;
        }
    }

}