<?php


namespace app\controller;


use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\service\ShortMessageService;
use app\service\Sms;
use think\Exception;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Log;

class ShortMessage extends BaseController
{

    /**
     * @param Request $request
     * @return \think\Response
     */
    public function createTest(Request $request)
    {
        $param = $request->param();
        $admin_id =  $request->header('vinehoo-uid');#获取操作人
        if (empty($admin_id)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '未获取到用户ID');
        }
        $param['admin_id'] = $admin_id;
        if ( !isset($param['sms_category']) || empty($param['sms_category'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '短信分类不能为空');
        }
        if ( !isset($param['sms_platform']) || empty($param['sms_platform'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '短信平台不能为空');
        }
        if ( !isset($param['sms_content']) || empty($param['sms_content'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '短信内容不能为空');
        }
        if ( !isset($param['sms_schedule']) || empty($param['sms_schedule'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '短发送时间不能为空');
        }
        if ( !isset($param['sms_phonestr']) || empty($param['sms_phonestr'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, 'redis储存标识不能为空');
        }
        $admin = getAdminInfo($param['admin_id']);
        $redis = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        $redis->auth(env('CACHE.PASSWORD'));
        $redis->select(14);

        try {
            $phonelist =$redis->zRange($param['sms_phonestr'],0,-1);
            #设置失效时间 发送时间一小时后失效
            $invalid_time = strtotime($param['sms_schedule'])+60*60;
            $redis->expireAt($param['sms_phonestr'],$invalid_time);
            $data['sms_category'] = $param['sms_category'];
            $data['sms_platform'] = $param['sms_platform'];
            $data['sms_scene'] = isset($param['sms_scene'])?$param['sms_scene']:1;
            $data['sms_creator'] = isset($admin[$param['admin_id']])?$admin[$param['admin_id']]['realname']:'';
            $data['sms_content'] = $param['sms_content'];
            $data['sms_phonestr'] = $param['sms_phonestr'];
            $data['sms_count'] = empty($phonelist)?0:count($phonelist);
            $data['sms_status'] = 1;
            $data['sms_created'] = date("Y-m-d H:i:s", time());
            $data['sms_schedule'] = $param['sms_schedule'];
            $data['sms_type'] = $param['sms_type'];
            $data['sms_template'] = isset($param['sms_template'])?$param['sms_template']:1;
            $redis->del($data['sms_phonestr']);
            redisBatchAdd($data['sms_phonestr'],$phonelist);
            $message_Id = Db::name('messages')->insertGetId($data);
            //推送钉钉接口 header里面自带dingtalk-dept-id,dingtalk-uid参数
            $result = dingdingCreate($message_Id);
            if($result!==true){
                return throwResponse([], ErrorCode::PARAM_ERROR, $result);
            }
        } catch (\Exception $e) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $e->getMessage());
        }
        return throwResponse($result, 0, '操作成功');
    }
    /**
     * @param Request $request
     * @return \think\Response
     */
    public function create(Request $request)
    {
        $param = $request->param();
        $admin_id =  $request->header('vinehoo-uid');#获取操作人
        if (empty($admin_id)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '未获取到用户ID');
        }
        $dept_id = request()->header('dingtalk-dept-id');
        $dingtalk_uid = request()->header('dingtalk-uid');
        if(empty($dept_id) || empty($dingtalk_uid)){
            return throwResponse([], ErrorCode::PARAM_ERROR, '请传入发起人钉钉部门id和钉钉用户uid');
        }
        $param['admin_id'] = $admin_id;
        if ( !isset($param['sms_category']) || empty($param['sms_category'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '短信分类不能为空');
        }
        if ( !isset($param['sms_platform']) || empty($param['sms_platform'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '短信平台不能为空');
        }
        if ( !isset($param['sms_content']) || empty($param['sms_content'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '短信内容不能为空');
        }

        // 短地址相关参数验证
        if (isset($param['url']) && !empty($param['url'])) {
            // 如果传入了URL参数，验证内容中必须包含该URL
            if (strpos($param['sms_content'], $param['url']) === false) {
                return throwResponse([], ErrorCode::PARAM_ERROR, '短信内容中必须包含传入的URL地址');
            }
            // 验证URL格式
            if (!filter_var($param['url'], FILTER_VALIDATE_URL)) {
                return throwResponse([], ErrorCode::PARAM_ERROR, 'URL格式不正确');
            }
        }
        if ( !isset($param['sms_schedule']) || empty($param['sms_schedule'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '短发送时间不能为空');
        }
        if ( !isset($param['send_type']) || empty($param['send_type'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '发送对象不能为空');
        }
        if ($param['send_type'] == 3 && ( !isset($param['sms_phonestr']) || empty($param['sms_phonestr']))) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '选择导入用户时 redis储存标识不能为空 ');
        }
        if ($param['send_type'] == 1 && (!isset($param['search_criteria']) || empty($param['search_criteria']))) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '选择指定用户时 筛选条件不能为空');
        }
        if ($param['send_type'] == 1 &&!empty($param['search_criteria']) ){
           if (isset($param['search_criteria']['is_ts']) && is_numeric($param['search_criteria']['is_ts'])){
               if (empty($param['search_criteria']['period']) || empty($param['search_criteria']['stime']) || empty($param['search_criteria']['etime']) || empty($param['search_criteria']['product_type'])) {
                   return throwResponse([], ErrorCode::PARAM_ERROR, '暂存筛选数据较多 请搭配其他筛选条件操作');
               }
           }
        }
        $search_criteria = empty($param['search_criteria'])?[]:$param['search_criteria'];
        if (!empty($search_criteria)){
            $search_criteria['send_type'] = $param['send_type'];
        }
        $admin = getAdminInfo($param['admin_id']);
        try {
            $data['sms_category'] = $param['sms_category'];
            $data['sms_platform'] = $param['sms_platform'];
            $data['sms_scene'] = isset($param['sms_scene'])?$param['sms_scene']:1;
            $data['sms_creator'] = isset($admin[$param['admin_id']])?$admin[$param['admin_id']]['realname']:'';
            $data['sms_content'] = $param['sms_content'];
            $data['sms_phonestr'] = isset($param['sms_phonestr'])?$param['sms_phonestr']:'';
            $data['sms_count'] = isset($param['sms_count'])?$param['sms_count']:0;
            $data['sms_status'] = 1;
            $data['sms_created'] = date("Y-m-d H:i:s", time());
            $data['sms_schedule'] = $param['sms_schedule'];
            $data['sms_type'] = $param['sms_type'];
            $data['send_type'] = $param['send_type'];
            $data['search_criteria'] = json_encode($search_criteria);
            $data['sms_template'] = isset($param['sms_template'])?$param['sms_template']:1;

            // 保存短地址相关参数
            $data['sms_url'] = isset($param['url']) ? $param['url'] : '';
            $data['sms_url_days'] = isset($param['days']) ? intval($param['days']) : 5;

            #如果接受人数为0去拿人数
            if($data['sms_count'] == 0){
                $searchData = $search_criteria;
                $searchData['send_type'] = $param['send_type'];
                $searchData['is_total'] = 1;
                $totalInfo = (new ShortMessageService())->getOrderSendSmsPhoneTotal($searchData,false);
                $data['sms_count'] = $totalInfo['total'];
            }

            $message_Id = Db::name('messages')->insertGetId($data);
            //推送钉钉接口 header里面自带dingtalk-dept-id,dingtalk-uid参数
            $result = dingdingCreate($message_Id);
            if($result!==true){
                return throwResponse([], ErrorCode::PARAM_ERROR, $result);
            }
        } catch (\Exception $e) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $e->getMessage());
        }
        return throwResponse($message_Id, 0, '操作成功');
    }

    /**
     * @Describe:添加短信和个性短信
     * <AUTHOR>
     * @Date 2021/9/16 11:38
     * @param Request $request
     * @return \think\response\Json
     */
    public function create1(Request $request)
    {
        $param = $request->param();
        $admin_id =  $request->header('vinehoo-uid');#获取操作人
        if (empty($admin_id)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '未获取到用户ID');
        }
        #验证
        $rule = [
            'sms_platform'=>'integer',
            'sms_category'=>'integer',
            'sms_type'=>'integer',
            'sms_phonestr'=>'require',
            'sms_template'=>'integer',
            'sms_scene'=>'integer',
            'sms_content'=>'require',
            'sms_schedule'=>'date',
        ];
        $message = [
            'sms_platform'=>'短信发送方为整数',
            'sms_category'=>'短信分类为整数',
            'sms_type'=>'短信类型为整数',
            'sms_phonestr'=>'redis,key为必填项',
            'sms_template'=>'短信模板为整数',
            'sms_scene'=>'短信场景为整数',
            'sms_content'=>'短信类容为必填项',
            'sms_schedule'=>'定时发送为时间格式',
        ];
        $checkval = checkParam($param, $rule, $message);
        if( $checkval !== true){
            return json($checkval);
        }
        $admin = getAdminInfo($admin_id);
        $param['sms_creator'] = isset($admin[$admin_id])?$admin[$admin_id]['realname']:'';
        $redis = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        $redis->auth(env('CACHE.PASSWORD'));
        $redis->select(14);
        $phonelist =$redis->zRange($param['sms_phonestr'],0,-1);
        Db::startTrans();
        try {
//            $phonelist =$redis->zRange($param['sms_phonestr'],0,-1);
//            $phoneList = Cache::store('redis')->handler()->zrange($param['sms_phonestr'],0,-1);
//            echo count($phoneList);exit();
            #设置失效时间 发送时间一小时后失效
            $invalid_time = strtotime($param['sms_schedule'])+60*60;
            $redis->expireAt($param['sms_phonestr'],$invalid_time);
            $data['sms_category'] = $param['sms_category'];
            $data['sms_platform'] = $param['sms_platform'];
            $data['sms_scene'] = $param['sms_scene'];
            $data['sms_creator'] = $param['sms_creator'];
            $data['sms_content'] = $param['sms_content'];
            $data['sms_phonestr'] = $param['sms_phonestr'];
            $data['sms_count'] = empty($phonelist)?0:count($phonelist);
            $data['sms_status'] = 1;
            $data['sms_created'] = date("Y-m-d H:i:s", time());
            $data['sms_schedule'] = $param['sms_schedule'];
            $data['sms_type'] = $param['sms_type'];
            $data['sms_template'] = $param['sms_template'];
//            if($data['sms_type'] == 2){
//                //短信发送模板
//                $data['sms_content'] = '你好{name},你的注册电话是{phone},{name}你知道了吗?';
//                //下面是接口返回给我的数据
//                $sqldata = [
//                    ['name'=>'张一','phone'=>'18983762612'],
//                    ['name'=>'张二','phone'=>'18983762613'],
//                    ['name'=>'张三','phone'=>'18983762614'],
//                    ['name'=>'张四','phone'=>'18983762615'],
//                    ['name'=>'张五','phone'=>'18983762616'],
//                    ['name'=>'张六','phone'=>'18983762617'],
//                    ['name'=>'张七','phone'=>'18983762618'],
//                    ['name'=>'张八','phone'=>'18983762619'],
//                ];
//                $result = createPersonalData($data['sms_content'],$sqldata);
//                if($result['status'] === false){
//                    throw new Exception($result['msg']);
//                }
//                $data['sms_count'] = empty($result['phoneList'])?0:count($result['phoneList']);
//                $phonelist = $result['phoneList'];
//                redisBatchAdd($data['sms_phonestr'].'.content',$result['smsContentList']);
//            }
            //  Cache::store('redis')->handler()->del($data['sms_phonestr']);
            $redis->del($data['sms_phonestr']);
            redisBatchAdd($data['sms_phonestr'],$phonelist);
            $message_Id = Db::name('messages')->insertGetId($data);
            //推送钉钉接口 header里面自带dingtalk-dept-id,dingtalk-uid参数
            $result = dingdingCreate($message_Id);
            if($result!==true){
                $this->throwError($result);
            }
            Db::commit();
            return  $this->success(true,"操作成功");
        }catch (Exception $exception){
            Db::rollback();
            $returndata['error_code'] = 10001;
            $returndata['error_msg'] = $exception->getMessage();
            return throwResponse([], ErrorCode::PARAM_ERROR, $exception->getMessage());
            // return json($returndata);
        }
    }
    /**
     * @Describe:取消短信
     * <AUTHOR>
     * @Date 2021/9/16 11:38
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     */
    public function cancel(){
        $param = $this->request->post();
        $rule = [
            'message_id'=>'integer|require',
        ];
        $message = [
            'message_id.integer'=>'短信id为整数',
            'message_id.require'=>'短信id为必传',
        ];
        $checkval = checkParam($param, $rule, $message);
        if( $checkval !== true){
            return json($checkval);
        }

        $message_id = $param['message_id'];
        $result = Db::name('messages')->where([['id','=',$message_id]])->update(['sms_status'=>5]);
        if($result){
            $returndata['error_code'] = 0;
            $returndata['error_msg'] = '取消成功';
            return json($returndata);
        }else{
            $returndata['error_code'] = 1;
            $returndata['error_msg'] = '取消失败';
            return json($returndata);
        }

    }

    public function search(){
        $param = $this->request->post();
        $sms_scene = isset($param['sms_scene'])?$param['sms_scene']:"";
        $sms_category = isset($param['sms_category'])?$param['sms_category']:"";
        $sms_status = isset($param['sms_status'])?$param['sms_status']:"";
        $sms_creator = isset($param['sms_creator'])?$param['sms_creator']:"";
        $sms_content = isset($param['sms_content'])?$param['sms_content']:"";
        $page = isset($param['page'])?$param['page']:1;
        $page = $page>=1?$page:1;
        $limit = isset($param['limit'])?$param['limit']:10;
        $limit = $limit>=1?$limit:10;
        $where = [];
        if(!empty($sms_scene)){
            $where[] = ['sms_scene','=',$sms_scene];
        }
        if(!empty($sms_category)){
            $where[] = ['sms_category','=',$sms_category];
        }
        if(!empty($sms_status)){
            $where[] = ['sms_status','=',$sms_status];
        }
        if(!empty($sms_creator)){
            $where[] = ['sms_creator','like',"%$sms_creator%"];
        }
        if(!empty($sms_content)){
            $where[] = ['sms_content','like',"%$sms_content%"];
        }
        $DBresult = Db::name('messages')->where($where);
        $total = $DBresult->count();
        $smslist = $DBresult->page($page)->order('sms_created','desc')->limit($limit)->select();
        $data['error_code'] = 0;
        $data['error_msg'] = '';
        $data['data']['total'] = $total;
        $data['data']['page'] = $page;
        $data['data']['limit'] = $limit;
        $data['data']['list'] = $smslist;
        return json($data);
    }

    /**
     * 短信发送
     * @param Request $request
     * @throws \Exception
     */
    public function sendSms(Request $request)
    {
        $param = $request->param();
        if (!isset($param['telephone']) || empty($param['telephone'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '电话号码不能为空');
        }
        if (!isset($param['content']) || empty($param['content'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '短信内容不能为空');
        }
        if (!isset($param['type'])) {
           $param['type'] = 1;
        }
        $phoneList = explode(",",$param['telephone']);
        if (count($phoneList)>10000){
            return throwResponse([], ErrorCode::PARAM_ERROR, '电话号码最多10000个');
        }
        if (count($phoneList)>1000){
            $result = Sms::BatchTySendSMS($param['content'],$phoneList,$param['type'], '', 5, '');
        }else{
            $result = Sms::OneTySendSMS($param['content'],$phoneList,$param['type'], '', 5, '');
        }
        if($result && isset($result['code']) && $result['code'] == 0){
            return $this->success(true,"发送成功");
        }else{
            return throwResponse([], -1,$result);
        }


    }

    /**
     * 语音发送
     * @param Request $request
     * @return mixed|\think\Response
     */
    public function voiceSend(Request $request)
    {
        $param = $request->param();
        if (!isset($param['sub_order_no']) || empty($param['sub_order_no'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '订单号不能为空');
        }
        if (!isset($param['telephone']) || empty($param['telephone'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '电话号码不能为空');
        }
        $result = Sms::voiceSend($param);
        if ($result && isset($result['code']) && $result['code'] == 2){
            return  $this->success(['voiceid'=>$result['voiceid']],$result['msg']);
        }else{
            return throwResponse(['voiceid'=>$result['voiceid']], $result['code'], $result['msg']);
        }

    }

    public function oneTySendSMS(Request $request)
    {
        $param = $request->param();
        if (!isset($param['telephone']) || empty($param['telephone'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '电话号码不能为空');
        }
        $phoneList = explode(",",$param['telephone']);
        if (count($phoneList)>1000){
            return throwResponse([], ErrorCode::PARAM_ERROR, '电话号码最多1000个');
        }
        if (!isset($param['content']) || empty($param['content'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '短信内容不能为空');
        }
        if (!isset($param['type'])) {
            $param['type'] = 1;
        }
        $result = Sms::oneTySendSMS($param['content'],$phoneList,$param['type']);
        if ($result && isset($result['code']) && $result['code'] == 2){
            return  $this->success(['voiceid'=>$result['voiceid']],$result['msg']);
        }else{
            return throwResponse(['voiceid'=>$result['voiceid']], $result['code'], $result['msg']);
        }
    }

    /**
     * 队列-筛选用户数据
     * @param Request $request
     * @return \think\Response|\think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function sendUserSms(Request $request)
    {
        $param = $request->param();
        Log::info("短信发送请求参数：".json_encode($param));
        if (!isset($param['telephone']) || empty($param['telephone'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '电话号码不能为空');
        }
        if (!isset($param['content']) || empty($param['content'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '短信内容不能为空');
        }
        if (!isset($param['type'])) {
            $param['type'] = 2; // 1：通知 2：营销(目前笔记只有一个短信模板,参数type没传值,直接修改默认为2)
        }
        if (count($param['telephone'])>10000){
            return throwResponse([], ErrorCode::PARAM_ERROR, '电话号码最多10000个');
        }
        $result = Sms::BatchTySendSMS($param['content'],$param['telephone'],$param['type'], '', 5, '');
        if($result && isset($result['code']) && $result['code'] == 0){
            #默认短信发送
            if (isset($param['default_telephone']) && !empty($param['default_telephone'])) {
                Sms::OneTySendSMS($param['content'],[$param['default_telephone']],$param['type'], '', 5, '');
            }
            return $this->success(true,"发送成功");
        }else{
            return throwResponse([], -1,$result);
        }
    }

    /**
     * 方法描述：短信发送 笔记使用
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2024/3/18 17:42
     * @param Request $request
     * @return \think\Response|\think\response\Json
     */
    public function sendSmsNote(Request $request)
    {
        $param = $request->param();
        if (!isset($param['telephone']) || empty($param['telephone'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '电话号码不能为空');
        }
        $phoneList = explode(",",$param['telephone']);
        if (count($phoneList)>1000){
            return throwResponse([], ErrorCode::PARAM_ERROR, '电话号码最多1000个');
        }
        if (!isset($param['content']) || empty($param['content'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '短信内容不能为空');
        }
        if (!isset($param['type'])) {
            $param['type'] = 2;
        }
        $result = Sms::OneTySendSMSNote($param['content'],$phoneList,$param['type']);
        if ($result && isset($result['code']) && $result['code'] == 0){
            return  $this->success();
        }else{
            return throwResponse([], -1, $result);
        }
    }


}